import { _decorator, Component, EventTouch, instantiate, Node, Vec3 } from 'cc';
import { UI } from '../../common';
import { DecorationMgr } from '../logic/DecorationMgr';
import { DefaultRoomTemplates } from '../logic/DefaultRoomTemplates';
import { GridState, RoomTemplate } from '../logic/DecorationDefine';
import { GridComp } from '../prefab/GridComp';
const { ccclass, property } = _decorator;

@ccclass('RoomComp')
export class RoomComp extends Component {
    @property({ type: Node, displayName: "", tooltip: "" })
    public roomANode!: Node;

    @property({ type: Node, displayName: "", tooltip: "" })
    public roomBNode!: Node;

    @property({ type: Node, displayName: "", tooltip: "" })
    public roomCNode!: Node;

    @property({ type: Node, displayName: "", tooltip: "" })
    public roomDNode!: Node;



    private roomA!: RoomTemplate;
    private roomB!: RoomTemplate;
    private roomC!: RoomTemplate;
    private roomD!: RoomTemplate;
    start() {
        this.addEvents();
        DecorationMgr.instance.init();
        //创建房间  
        this.roomA = DefaultRoomTemplates.getAllTemplates()[0];
        this.roomB = DefaultRoomTemplates.getAllTemplates()[1];
        this.roomC = DefaultRoomTemplates.getAllTemplates()[2];
        this.roomD = DefaultRoomTemplates.getAllTemplates()[3];

        //this.roomA.layout 

        this.initView();
    }

    addEvents() {

    }

    private initView() {
        let ronnNodeList = [this.roomANode, this.roomBNode, this.roomCNode, this.roomDNode];
        let templatesList = [this.roomA, this.roomB, this.roomC, this.roomD];
        console.log("templatesList =", templatesList);
          templatesList.forEach((v, k) => {
             let node = ronnNodeList[k];
             for (let y = 0; y < v.layout.length; y++) {
                 let rowNode = node.getChildByName("row" + y)!;
                 for (let x = 0; x < v.layout[y].length; x++) {
                     let gzNode = rowNode.getChildByName("gezi" + x)!;
                     let gridComp = gzNode.getComponent(GridComp)!;
                     gridComp.decorationType = v.layout[y][x];
                     gzNode.active = (gridComp.decorationType !== GridState.Obstacle);
                 }
             }
         })  
    }

    private moveTarget!: Node;
    private initPos: Vec3 = new Vec3(0, 0, 0);
    private moveStart(evt: EventTouch) {
        this.moveTarget = evt.currentTarget;
        this.initPos.set(this.moveTarget.worldPosition);
        this.node.on(Node.EventType.TOUCH_MOVE, this.move, this);
        this.node.on(Node.EventType.TOUCH_END, this.moveEnd, this);
        this.node.on(Node.EventType.TOUCH_CANCEL, this.moveEnd, this);
    }

    private move(evt: EventTouch) {

    }

    private moveEnd() {
        this.node.off(Node.EventType.TOUCH_MOVE, this.move, this);
        this.node.off(Node.EventType.TOUCH_END, this.moveEnd, this);
        this.node.off(Node.EventType.TOUCH_CANCEL, this.moveEnd, this);
    }

}


