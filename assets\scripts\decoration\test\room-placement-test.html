<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>房间摆放系统测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            padding-bottom: 160px; /* 为底部信息面板留出空间 */
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .info-panel {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            height: 120px;
            z-index: 1000;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }
        
        .info-title {
            font-weight: bold;
            color: #495057;
            margin-bottom: 8px;
        }
        
        .info-content {
            color: #6c757d;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .room-container {
            display: flex;
            gap: 60px; /* 增大间距强调房间不相邻 */
            justify-content: center;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        
        .room {
            border: 3px solid #333;
            background: #fff;
            position: relative;
            border-radius: 8px;
            padding: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .room-title {
            text-align: center;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .grid {
            display: grid;
            gap: 1px;
            background: #ccc;
            border: 1px solid #999;
        }
        
        .grid-cell {
            width: 30px;
            height: 30px;
            border: 1px solid #ddd;
            position: relative;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .grid-cell:hover {
            border-color: #007bff;
            box-shadow: 0 0 5px rgba(0,123,255,0.3);
        }
        
        /* 格子状态样式 */
        .grid-empty {
            background-color: #f8f9fa;
        }
        
        .grid-wall-adjacent {
            background-color: #e9ecef;
            border-color: #6c757d;
        }
        
        .grid-obstacle {
            background-color: #343a40;
        }
        
        .grid-occupied {
            background-color: #28a745;
        }
        
        /* 家具样式 */
        .furniture {
            position: absolute;
            border: 2px solid #007bff;
            background: rgba(0,123,255,0.3);
            border-radius: 3px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            color: #004085;
            z-index: 10;
        }
        
        .furniture:hover {
            background: rgba(0,123,255,0.5);
            border-color: #0056b3;
            box-shadow: 0 2px 8px rgba(0,123,255,0.4);
            transform: scale(1.05);
        }
        
        .furniture.wall-decoration {
            background: rgba(220,53,69,0.3);
            border-color: #dc3545;
            color: #721c24;
        }
        
        .furniture.wall-decoration:hover {
            background: rgba(220,53,69,0.5);
            border-color: #c82333;
        }
        
        .controls {
            margin-top: 20px;
            text-align: center;
        }

        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 5px;
            font-size: 14px;
        }

        .btn:hover {
            background: #0056b3;
        }

        .btn.secondary {
            background: #6c757d;
        }

        .btn.secondary:hover {
            background: #545b62;
        }

        .btn.success {
            background: #28a745;
        }

        .btn.success:hover {
            background: #218838;
        }

        .score-panel {
            background: #e9ecef;
            border: 1px solid #ced4da;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }

        .score-title {
            font-weight: bold;
            color: #495057;
            margin-bottom: 10px;
            font-size: 16px;
        }

        .score-details {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            gap: 10px;
        }

        .score-item {
            background: white;
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            min-width: 80px;
        }

        .score-label {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 2px;
        }

        .score-value {
            font-size: 16px;
            font-weight: bold;
            color: #495057;
        }

        .total-score {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .total-score .score-label {
            color: #cce7ff;
        }

        .total-score .score-value {
            color: white;
            font-size: 18px;
        }

        .furniture-selector {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .selector-title {
            font-weight: bold;
            color: #495057;
            margin-bottom: 10px;
        }

        .furniture-options {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .furniture-option {
            background: white;
            border: 2px solid #dee2e6;
            border-radius: 4px;
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
        }

        .furniture-option:hover {
            border-color: #007bff;
            background: #e7f3ff;
        }

        .furniture-option.selected {
            border-color: #007bff;
            background: #007bff;
            color: white;
        }

        .mode-selector {
            background: #e9ecef;
            border: 1px solid #ced4da;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 20px;
            text-align: center;
        }

        .mode-btn {
            background: white;
            border: 1px solid #ced4da;
            border-radius: 4px;
            padding: 6px 12px;
            margin: 0 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .mode-btn:hover {
            background: #f8f9fa;
        }

        .mode-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .furniture.selected {
            border-color: #ffc107;
            border-width: 3px;
            box-shadow: 0 0 10px rgba(255,193,7,0.5);
        }

        .grid-cell.highlight {
            background-color: rgba(0,123,255,0.2) !important;
            border-color: #007bff !important;
        }
        
        .legend {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 15px;
            font-size: 12px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .legend-color {
            width: 16px;
            height: 16px;
            border: 1px solid #999;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>房间摆放系统测试</h1>
            <p>选择操作模式：摆放模式点击格子放置家具，移除/旋转模式直接点击家具操作</p>
            <p style="color: #6c757d; font-size: 14px;">注意：两个房间不相邻，不能跨房间摆放家具，共用一个评分系统</p>
        </div>

        <div class="mode-selector">
            <span style="margin-right: 15px; font-weight: bold;">操作模式:</span>
            <button class="mode-btn active" onclick="setMode('place')" id="placeMode">摆放模式</button>
            <button class="mode-btn" onclick="setMode('remove')" id="removeMode">移除模式</button>
            <button class="mode-btn" onclick="setMode('rotate')" id="rotateMode">旋转模式</button>
        </div>

        <div class="furniture-selector">
            <div class="selector-title">选择家具类型 (摆放模式下有效):</div>
            <div class="furniture-options" id="furnitureOptions">
                <div class="furniture-option selected" data-template-id="1">现代椅子 (1×1)</div>
                <div class="furniture-option" data-template-id="2">古典床 (2×1)</div>
                <div class="furniture-option" data-template-id="3">工业风桌子 (2×2)</div>
                <div class="furniture-option" data-template-id="4">简约台灯 (1×1)</div>
                <div class="furniture-option" data-template-id="5">自然风沙发 (2×1)</div>
                <div class="furniture-option" data-template-id="6">古典书柜 (2×2)</div>
                <div class="furniture-option" data-template-id="11">现代壁画 (1×1 挂饰)</div>
                <div class="furniture-option" data-template-id="12">古典挂钟 (1×1 挂饰)</div>
            </div>
        </div>

        <div class="score-panel">
            <div class="score-title">房间装饰评分</div>
            <div class="score-details" id="scoreDetails">
                <div class="score-item total-score">
                    <div class="score-label">总评分</div>
                    <div class="score-value" id="totalScore">0</div>
                </div>
                <div class="score-item">
                    <div class="score-label">主题评分</div>
                    <div class="score-value" id="themeScore">0</div>
                </div>
                <div class="score-item">
                    <div class="score-label">数量评分</div>
                    <div class="score-value" id="quantityScore">0</div>
                </div>
                <div class="score-item">
                    <div class="score-label">价值评分</div>
                    <div class="score-value" id="valueScore">0</div>
                </div>
                <div class="score-item">
                    <div class="score-label">布局评分</div>
                    <div class="score-value" id="layoutScore">0</div>
                </div>
                <div class="score-item">
                    <div class="score-label">相邻评分</div>
                    <div class="score-value" id="adjacentScore">0</div>
                </div>
                <div class="score-item">
                    <div class="score-label">主导主题</div>
                    <div class="score-value" id="dominantTheme">无</div>
                </div>
            </div>
        </div>
        
        <div class="info-panel">
            <div class="info-title">信息面板</div>
            <div class="info-content" id="infoContent">
                将鼠标悬停在房间格子或家具上查看详细信息...
            </div>
        </div>
        
        <div class="room-container">
            <div>
                <div class="room-title">房间A (10×10)</div>
                <div class="room" id="roomA">
                    <div class="grid" id="gridA" style="grid-template-columns: repeat(10, 30px);"></div>
                </div>
            </div>
            
            <div>
                <div class="room-title">房间B (5×4)</div>
                <div class="room" id="roomB">
                    <div class="grid" id="gridB" style="grid-template-columns: repeat(5, 30px);"></div>
                </div>
            </div>
        </div>
        
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color grid-empty"></div>
                <span>空地 (0)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color grid-wall-adjacent"></div>
                <span>邻墙 (1)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color grid-obstacle"></div>
                <span>障碍 (2)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color grid-occupied"></div>
                <span>占用 (3)</span>
            </div>
        </div>
        
        <div class="controls">
            <button class="btn success" onclick="calculateScore()">计算评分</button>
            <button class="btn secondary" onclick="clearAllFurniture()">清空家具</button>
            <button class="btn secondary" onclick="resetRooms()">重置房间</button>
        </div>
    </div>

    <script>
        // 房间摆放系统测试脚本
        // 模拟房间摆放系统的核心数据结构和功能
        
        // 枚举定义
        const GridState = {
            Empty: 0,          // 空地
            WallAdjacent1: 1,  // 邻墙格子1
            WallAdjacent2: 2,  // 邻墙格子2
            Obstacle: 3,       // 障碍
            Occupied: 4        // 占用
        };
        
        const FurnitureType = {
            Small: 1,           // 1×1 小型家具
            Medium: 2,          // 2×1 中型家具  
            Large: 3,           // 2×2 大型家具
            WallDecoration: 4   // 挂饰类家具
        };
        
        const FurnitureTheme = {
            Modern: 1,      // 现代风格
            Classic: 2,     // 古典风格
            Natural: 3,     // 自然风格
            Industrial: 4,  // 工业风格
            Minimalist: 5   // 简约风格
        };
        
        const Rotation = {
            Deg0: 0,
            Deg90: 90
        };
        
        // 房间配置（两个房间不相邻，独立布局）
        const roomConfigs = {
            roomA: {
                id: 'roomA',
                name: '房间A',
                size: { x: 10, y: 10 },
                layout: [
                    [3,3,3,3,3,0,0,3,3,3],
                    [3,3,3,3,3,0,0,0,3,3],
                    [0,0,0,3,3,0,0,0,0,3],
                    [0,0,0,3,3,0,0,0,0,0],
                    [0,0,0,0,0,0,0,0,0,0],
                    [0,0,0,0,0,0,0,0,0,2],
                    [0,0,0,0,0,0,0,0,0,2],
                    [0,0,0,0,0,0,0,0,0,3],
                    [3,0,0,0,0,0,0,0,3,3],
                    [3,3,0,0,0,0,0,3,3,3]
                ],
                furnitures: []
            },
            roomB: {
                id: 'roomB',
                name: '房间B',
                size: { x: 5, y: 4 },
                layout: [
                    [0,0,0,0,2],
                    [0,0,0,0,2],
                    [0,0,0,0,2],
                    [0,0,0,0,2]
                ],
                furnitures: []
            }
        };
        
        // 家具模板（与 TypeScript 数据文件保持同步）
        const furnitureTemplates = [
            {
                id: 1,
                name: '现代椅子',
                type: FurnitureType.Small,
                baseSize: { x: 1, y: 1 },
                properties: {
                    theme: FurnitureTheme.Modern,
                    level: 1,
                    value: 8,
                    beauty: 6,
                    isWallDecoration: false
                }
            },
            {
                id: 2,
                name: '古典床',
                type: FurnitureType.Medium,
                baseSize: { x: 2, y: 1 },
                properties: {
                    theme: FurnitureTheme.Classic,
                    level: 2,
                    value: 15,
                    beauty: 8,
                    isWallDecoration: false
                }
            },
            {
                id: 3,
                name: '工业风桌子',
                type: FurnitureType.Large,
                baseSize: { x: 2, y: 2 },
                properties: {
                    theme: FurnitureTheme.Industrial,
                    level: 2,
                    value: 12,
                    beauty: 5,
                    isWallDecoration: false
                }
            },
            {
                id: 4,
                name: '简约台灯',
                type: FurnitureType.Small,
                baseSize: { x: 1, y: 1 },
                properties: {
                    theme: FurnitureTheme.Minimalist,
                    level: 1,
                    value: 6,
                    beauty: 7,
                    isWallDecoration: false
                }
            },
            {
                id: 5,
                name: '自然风沙发',
                type: FurnitureType.Medium,
                baseSize: { x: 2, y: 1 },
                properties: {
                    theme: FurnitureTheme.Natural,
                    level: 3,
                    value: 20,
                    beauty: 9,
                    isWallDecoration: false
                }
            },
            {
                id: 6,
                name: '古典书柜',
                type: FurnitureType.Large,
                baseSize: { x: 2, y: 2 },
                properties: {
                    theme: FurnitureTheme.Classic,
                    level: 3,
                    value: 25,
                    beauty: 10,
                    isWallDecoration: false
                }
            },
            {
                id: 11,
                name: '现代壁画',
                type: FurnitureType.WallDecoration,
                baseSize: { x: 1, y: 1 },
                properties: {
                    theme: FurnitureTheme.Modern,
                    level: 2,
                    value: 12,
                    beauty: 10,
                    isWallDecoration: true
                }
            },
            {
                id: 12,
                name: '古典挂钟',
                type: FurnitureType.WallDecoration,
                baseSize: { x: 1, y: 1 },
                properties: {
                    theme: FurnitureTheme.Classic,
                    level: 2,
                    value: 10,
                    beauty: 8,
                    isWallDecoration: true
                }
            }
        ];
        
        // 全局变量
        let infoContent = null;
        let selectedFurniture = null; // 当前选中的家具
        let currentMode = 'place'; // 当前操作模式：place, remove, rotate
        let selectedTemplateId = 1; // 当前选中的家具模板ID

        // 评分权重配置
        const scoreWeights = {
            theme: 0.4,      // 主题权重
            quantity: 0.2,   // 数量权重
            value: 0.25,     // 价值权重
            layout: 0.25,    // 布局权重
            adjacent: 0.1    // 相邻加成权重
        };

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            infoContent = document.getElementById('infoContent');
            initializeRooms();
            calculateScore(); // 初始计算评分
            initializeFurnitureSelector();
        });

        // 初始化房间
        function initializeRooms() {
            // 渲染房间（使用预定义的布局）
            renderRoom('roomA', roomConfigs.roomA);
            renderRoom('roomB', roomConfigs.roomB);
        }


        // 渲染房间
        function renderRoom(roomId, roomConfig) {
            const gridElement = document.getElementById(`grid${roomId.charAt(roomId.length - 1).toUpperCase()}`);
            gridElement.innerHTML = '';

            const { size, layout, furnitures } = roomConfig;

            // 创建格子
            for (let y = 0; y < size.y; y++) {
                for (let x = 0; x < size.x; x++) {
                    const cell = document.createElement('div');
                    cell.className = 'grid-cell';
                    cell.dataset.x = x;
                    cell.dataset.y = y;
                    cell.dataset.roomId = roomId;

                    // 设置格子状态样式
                    const gridState = layout[y][x];
                    switch (gridState) {
                        case GridState.Empty:
                            cell.classList.add('grid-empty');
                            break;
                        case GridState.WallAdjacent1:
                        case GridState.WallAdjacent2:
                            cell.classList.add('grid-wall-adjacent');
                            break;
                        case GridState.Obstacle:
                            cell.classList.add('grid-obstacle');
                            break;
                        case GridState.Occupied:
                            cell.classList.add('grid-occupied');
                            break;
                    }

                    // 添加鼠标事件
                    cell.addEventListener('mouseenter', function() {
                        showGridInfo(roomConfig, x, y, gridState);
                        if (currentMode === 'place') {
                            cell.classList.add('highlight');
                        }
                    });

                    cell.addEventListener('mouseleave', function() {
                        clearInfo();
                        cell.classList.remove('highlight');
                    });

                    // 添加点击事件
                    cell.addEventListener('click', function() {
                        handleGridClick(roomConfig, x, y);
                    });

                    gridElement.appendChild(cell);
                }
            }

            // 渲染家具
            renderFurnitures(roomId, roomConfig);
        }

        // 渲染家具
        function renderFurnitures(roomId, roomConfig) {
            const roomElement = document.getElementById(roomId);

            // 清除现有家具元素
            const existingFurnitures = roomElement.querySelectorAll('.furniture');
            existingFurnitures.forEach(f => f.remove());

            // 渲染每个家具
            roomConfig.furnitures.forEach(furniture => {
                const template = furnitureTemplates.find(t => t.id === furniture.templateId);
                if (!template) return;

                const furnitureElement = document.createElement('div');
                furnitureElement.className = 'furniture';
                furnitureElement.dataset.furnitureId = furniture.id;

                if (template.properties.isWallDecoration) {
                    furnitureElement.classList.add('wall-decoration');
                }

                if (selectedFurniture === furniture.id) {
                    furnitureElement.classList.add('selected');
                }

                // 计算位置和尺寸
                const cellSize = 30;
                const gap = 1;
                const currentSize = getRotatedSize(template.baseSize, furniture.rotation);

                furnitureElement.style.left = `${furniture.position.x * (cellSize + gap) + gap}px`;
                furnitureElement.style.top = `${furniture.position.y * (cellSize + gap) + gap}px`;
                furnitureElement.style.width = `${currentSize.x * cellSize + (currentSize.x - 1) * gap}px`;
                furnitureElement.style.height = `${currentSize.y * cellSize + (currentSize.y - 1) * gap}px`;

                // 显示家具名称
                furnitureElement.textContent = template.name;

                // 添加鼠标事件
                furnitureElement.addEventListener('mouseenter', function(e) {
                    e.stopPropagation();
                    showFurnitureInfo(furniture, template);
                });

                furnitureElement.addEventListener('mouseleave', function() {
                    clearInfo();
                });

                // 添加右键旋转功能
                furnitureElement.addEventListener('contextmenu', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    rotateFurniture(furniture.id, roomConfig);
                });

                // 添加点击功能（根据当前模式执行不同操作）
                furnitureElement.addEventListener('click', function(e) {
                    e.stopPropagation();
                    handleFurnitureClick(furniture, roomConfig);
                });

                roomElement.appendChild(furnitureElement);
            });
        }

        // 显示格子信息
        function showGridInfo(roomConfig, x, y, gridState) {
            const stateNames = {
                [GridState.Empty]: '空地',
                [GridState.WallAdjacent1]: '邻墙格子1',
                [GridState.WallAdjacent2]: '邻墙格子2',
                [GridState.Obstacle]: '障碍物',
                [GridState.Occupied]: '已占用'
            };

            const stateName = stateNames[gridState] || '未知';
            const stateColor = getGridStateColor(gridState);

            infoContent.innerHTML = `
                <div><strong>格子信息</strong></div>
                <div>房间: ${roomConfig.name}</div>
                <div>位置: (${x}, ${y})</div>
                <div>状态: <span style="color: ${stateColor}; font-weight: bold;">${stateName} (${gridState})</span></div>
                <div>描述: ${getGridStateDescription(gridState)}</div>
            `;
        }

        // 显示家具信息
        function showFurnitureInfo(furniture, template) {
            const themeNames = {
                [FurnitureTheme.Modern]: '现代风格',
                [FurnitureTheme.Classic]: '古典风格',
                [FurnitureTheme.Natural]: '自然风格',
                [FurnitureTheme.Industrial]: '工业风格',
                [FurnitureTheme.Minimalist]: '简约风格'
            };

            const typeNames = {
                [FurnitureType.Small]: '小型家具 (1×1)',
                [FurnitureType.Medium]: '中型家具 (2×1)',
                [FurnitureType.Large]: '大型家具 (2×2)',
                [FurnitureType.WallDecoration]: '挂饰类家具'
            };

            const currentSize = getRotatedSize(template.baseSize, furniture.rotation);
            const themeName = themeNames[template.properties.theme] || '未知主题';
            const typeName = typeNames[template.type] || '未知类型';

            infoContent.innerHTML = `
                <div><strong>家具信息</strong></div>
                <div>名称: <span style="color: #007bff; font-weight: bold;">${template.name}</span></div>
                <div>ID: ${furniture.id}</div>
                <div>位置: (${furniture.position.x}, ${furniture.position.y})</div>
                <div>旋转: ${furniture.rotation}°</div>
                <div>尺寸: ${currentSize.x}×${currentSize.y} (原始: ${template.baseSize.x}×${template.baseSize.y})</div>
                <div>类型: ${typeName}</div>
                <div>主题: ${themeName}</div>
                <div>等级: ${template.properties.level}</div>
                <div>价值: ${template.properties.value}</div>
                <div>美观度: ${template.properties.beauty}</div>
                <div>挂饰: ${template.properties.isWallDecoration ? '是' : '否'}</div>
                <div>放置时间: ${new Date(furniture.placedTime).toLocaleTimeString()}</div>
            `;
        }

        // 清除信息显示
        function clearInfo() {
            infoContent.innerHTML = '将鼠标悬停在房间格子或家具上查看详细信息...';
        }

        // 获取格子状态颜色
        function getGridStateColor(gridState) {
            switch (gridState) {
                case GridState.Empty: return '#28a745';
                case GridState.WallAdjacent1: return '#6c757d';
                case GridState.WallAdjacent2: return '#6c757d';
                case GridState.Obstacle: return '#dc3545';
                case GridState.Occupied: return '#007bff';
                default: return '#6c757d';
            }
        }

        // 获取格子状态描述
        function getGridStateDescription(gridState) {
            switch (gridState) {
                case GridState.Empty: return '可以放置普通家具';
                case GridState.WallAdjacent1: return '可以放置普通家具和挂饰类家具';
                case GridState.WallAdjacent2: return '可以放置普通家具和挂饰类家具';
                case GridState.Obstacle: return '不可放置任何家具';
                case GridState.Occupied: return '已被家具占用';
                default: return '未知状态';
            }
        }

        // 获取旋转后的尺寸
        function getRotatedSize(baseSize, rotation) {
            if (rotation === Rotation.Deg90) {
                return { x: baseSize.y, y: baseSize.x };
            }
            return { x: baseSize.x, y: baseSize.y };
        }

        // 生成家具ID
        function generateFurnitureId() {
            return `furniture_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        }



        // 旋转家具
        function rotateFurniture(furnitureId, roomConfig) {
            const furniture = roomConfig.furnitures.find(f => f.id === furnitureId);
            if (!furniture) return;

            const template = furnitureTemplates.find(t => t.id === furniture.templateId);
            if (!template) return;

            // 先移除当前占用
            updateGridOccupancy(roomConfig, furniture, false);

            // 计算新的旋转角度（只在0度和90度之间切换）
            const nextRotation = furniture.rotation === Rotation.Deg0 ? Rotation.Deg90 : Rotation.Deg0;

            // 检查旋转后是否可以放置
            if (canPlaceFurniture(roomConfig, template, furniture.position, nextRotation)) {
                furniture.rotation = nextRotation;
                furniture.currentSize = getRotatedSize(template.baseSize, nextRotation);

                // 更新占用状态
                updateGridOccupancy(roomConfig, furniture, true);

                // 重新渲染房间
                const roomId = roomConfig.id;
                renderRoom(roomId, roomConfig);

                // 重新计算评分
                calculateScore();

                console.log(`家具 ${template.name} 旋转到 ${nextRotation}°`);
            } else {
                // 恢复占用状态
                updateGridOccupancy(roomConfig, furniture, true);
                console.log(`家具 ${template.name} 无法旋转到 ${nextRotation}°`);
            }
        }

        // 选中家具
        function selectFurniture(furnitureId) {
            selectedFurniture = furnitureId;
            console.log(`选中家具: ${furnitureId}`);
        }

        // 处理家具点击事件
        function handleFurnitureClick(furniture, roomConfig) {
            switch (currentMode) {
                case 'place':
                    // 摆放模式下选中家具
                    selectFurniture(furniture.id);
                    break;
                case 'remove':
                    // 移除模式下直接移除家具
                    removeFurnitureById(furniture.id, roomConfig);
                    break;
                case 'rotate':
                    // 旋转模式下直接旋转家具
                    rotateFurniture(furniture.id, roomConfig);
                    break;
            }
        }

        // 根据家具ID移除家具
        function removeFurnitureById(furnitureId, roomConfig) {
            const furniture = roomConfig.furnitures.find(f => f.id === furnitureId);
            if (!furniture) {
                console.log(`未找到家具: ${furnitureId}`);
                return;
            }

            // 移除家具
            const index = roomConfig.furnitures.indexOf(furniture);
            roomConfig.furnitures.splice(index, 1);

            // 更新格子状态
            updateGridOccupancy(roomConfig, furniture, false);

            // 重新渲染房间
            const roomId = roomConfig.id;
            renderRoom(roomId, roomConfig);

            // 重新计算评分
            calculateScore();

            const template = furnitureTemplates.find(t => t.id === furniture.templateId);
            console.log(`移除了 ${template ? template.name : '家具'}`);
        }

        // 计算评分
        function calculateScore() {
            const allFurnitures = [
                ...roomConfigs.roomA.furnitures,
                ...roomConfigs.roomB.furnitures
            ];

            if (allFurnitures.length === 0) {
                updateScoreDisplay({
                    totalScore: 0,
                    themeScore: 0,
                    quantityScore: 0,
                    valueScore: 0,
                    layoutScore: 0,
                    adjacentScore: 0,
                    dominantTheme: '无'
                });
                return;
            }

            // 计算各项评分
            const themeScore = calculateThemeScore(allFurnitures);
            const quantityScore = calculateQuantityScore(allFurnitures.length);
            const valueScore = calculateValueScore(allFurnitures);
            const layoutScore = calculateLayoutScore(allFurnitures);
            const adjacentScore = calculateAdjacentScore(allFurnitures);

            // 计算总评分
            const totalScore = Math.round(
                themeScore * scoreWeights.theme +
                quantityScore * scoreWeights.quantity +
                valueScore * scoreWeights.value +
                layoutScore * scoreWeights.layout +
                adjacentScore * scoreWeights.adjacent
            );

            // 获取主导主题
            const dominantTheme = getDominantTheme(allFurnitures);

            const scoreDetails = {
                totalScore,
                themeScore: Math.round(themeScore),
                quantityScore: Math.round(quantityScore),
                valueScore: Math.round(valueScore),
                layoutScore: Math.round(layoutScore),
                adjacentScore: Math.round(adjacentScore),
                dominantTheme: getThemeName(dominantTheme)
            };

            updateScoreDisplay(scoreDetails);
            return scoreDetails;
        }

        // 计算主题评分
        function calculateThemeScore(furnitures) {
            if (furnitures.length === 0) return 0;

            const themeCount = new Map();
            furnitures.forEach(furniture => {
                const template = furnitureTemplates.find(t => t.id === furniture.templateId);
                if (template) {
                    const theme = template.properties.theme;
                    themeCount.set(theme, (themeCount.get(theme) || 0) + 1);
                }
            });

            // 找到主导主题
            let maxCount = 0;
            let dominantTheme = null;
            themeCount.forEach((count, theme) => {
                if (count > maxCount) {
                    maxCount = count;
                    dominantTheme = theme;
                }
            });

            // 主题一致性评分
            const consistencyScore = (maxCount / furnitures.length) * 100;

            // 主题多样性惩罚
            const diversityPenalty = Math.max(0, (themeCount.size - 2) * 10);

            return Math.max(0, consistencyScore - diversityPenalty);
        }

        // 计算数量评分
        function calculateQuantityScore(furnitureCount) {
            if (furnitureCount <= 5) {
                return furnitureCount * 10; // 每个家具10分，最高50分
            } else if (furnitureCount <= 10) {
                return 50 + (furnitureCount - 5) * 5; // 5个以上每个家具5分，最高75分
            } else {
                return 75; // 10个以上不再增长
            }
        }

        // 计算价值评分
        function calculateValueScore(furnitures) {
            if (furnitures.length === 0) return 0;

            const totalValue = furnitures.reduce((sum, furniture) => {
                const template = furnitureTemplates.find(t => t.id === furniture.templateId);
                return sum + (template ? template.properties.value : 0);
            }, 0);

            const averageValue = totalValue / furnitures.length;
            return Math.min(100, averageValue * 2); // 平均价值*2，最高100分
        }

        // 计算布局评分
        function calculateLayoutScore(furnitures) {
            if (furnitures.length === 0) return 0;

            let layoutScore = 0;

            // 计算房间利用率
            const totalRoomArea = (10 * 10) + (5 * 4); // 房间A + 房间B
            const occupiedArea = furnitures.reduce((sum, furniture) => {
                return sum + (furniture.currentSize.x * furniture.currentSize.y);
            }, 0);

            const utilization = occupiedArea / totalRoomArea;

            // 最佳利用率在20%-60%之间
            if (utilization >= 0.2 && utilization <= 0.6) {
                layoutScore += 30;
            } else if (utilization < 0.2) {
                layoutScore += utilization * 150; // 利用率过低扣分
            } else {
                layoutScore += Math.max(0, 30 - (utilization - 0.6) * 100); // 利用率过高扣分
            }

            // 家具分布评分
            const roomACount = roomConfigs.roomA.furnitures.length;
            const roomBCount = roomConfigs.roomB.furnitures.length;
            const distributionBalance = 1 - Math.abs(roomACount - roomBCount) / furnitures.length;
            layoutScore += distributionBalance * 20;

            return Math.max(0, Math.min(100, layoutScore));
        }

        // 计算相邻评分
        function calculateAdjacentScore(furnitures) {
            if (furnitures.length <= 1) return 0;

            let totalBonus = 0;
            const processedPairs = new Set(); // 避免重复计算同一对家具

            furnitures.forEach((furniture, index) => {
                const adjacentFurnitures = getAdjacentFurnitures(furniture, furnitures);

                adjacentFurnitures.forEach(adjacentFurniture => {
                    // 创建唯一的配对标识符，避免重复计算
                    const pairId = createPairId(furniture.id, adjacentFurniture.id);
                    if (processedPairs.has(pairId)) return;
                    processedPairs.add(pairId);

                    // 计算相邻奖励
                    const bonus = calculatePairBonus(furniture, adjacentFurniture);
                    totalBonus += bonus;
                });
            });

            return Math.min(100, totalBonus); // 相邻加成最高100分
        }

        // 获取与指定家具相邻的家具列表
        function getAdjacentFurnitures(targetFurniture, allFurnitures) {
            const adjacent = [];
            const targetPositions = getFurnitureOccupiedPositions(targetFurniture);

            allFurnitures.forEach(otherFurniture => {
                if (otherFurniture.id === targetFurniture.id) return;

                const otherPositions = getFurnitureOccupiedPositions(otherFurniture);

                // 检查是否相邻（任意一个格子相邻即可）
                const isAdjacent = targetPositions.some(targetPos =>
                    otherPositions.some(otherPos =>
                        arePositionsAdjacent(targetPos, otherPos)
                    )
                );

                if (isAdjacent) {
                    adjacent.push(otherFurniture);
                }
            });

            return adjacent;
        }

        // 获取家具占用的所有位置
        function getFurnitureOccupiedPositions(furniture) {
            const positions = [];
            const { position, currentSize } = furniture;

            for (let x = 0; x < currentSize.x; x++) {
                for (let y = 0; y < currentSize.y; y++) {
                    positions.push({ x: position.x + x, y: position.y + y });
                }
            }

            return positions;
        }

        // 检查两个位置是否相邻（四方向）
        function arePositionsAdjacent(pos1, pos2) {
            const dx = Math.abs(pos1.x - pos2.x);
            const dy = Math.abs(pos1.y - pos2.y);

            // 相邻条件：曼哈顿距离为1
            return (dx === 1 && dy === 0) || (dx === 0 && dy === 1);
        }

        // 计算两个相邻家具的奖励分数
        function calculatePairBonus(furniture1, furniture2) {
            const template1 = furnitureTemplates.find(t => t.id === furniture1.templateId);
            const template2 = furnitureTemplates.find(t => t.id === furniture2.templateId);

            if (!template1 || !template2) return 0;

            let bonus = 0;

            // 1. 同类型家具相邻奖励
            if (template1.type === template2.type) {
                bonus += 4; // 同类型+4分
            }

            // 2. 同主题家具相邻奖励
            if (template1.properties.theme === template2.properties.theme) {
                bonus += 3; // 同主题+3分
            }

            // 3. 和谐主题搭配奖励
            if (areThemesHarmonious(template1.properties.theme, template2.properties.theme)) {
                bonus += 2; // 和谐搭配+2分
            }

            // 4. 特殊组合奖励
            const specialBonus = calculateSpecialCombinationBonus(template1, template2);
            bonus += specialBonus;

            // 5. 尺寸搭配奖励
            const sizeBonus = calculateSizeMatchBonus(furniture1, furniture2);
            bonus += sizeBonus;

            return bonus;
        }

        // 检查两个主题是否和谐
        function areThemesHarmonious(theme1, theme2) {
            if (theme1 === theme2) return false; // 相同主题不算和谐搭配

            const harmoniousThemes = {
                [FurnitureTheme.Modern]: [FurnitureTheme.Minimalist, FurnitureTheme.Industrial],
                [FurnitureTheme.Classic]: [FurnitureTheme.Natural],
                [FurnitureTheme.Natural]: [FurnitureTheme.Classic, FurnitureTheme.Minimalist],
                [FurnitureTheme.Industrial]: [FurnitureTheme.Modern, FurnitureTheme.Minimalist],
                [FurnitureTheme.Minimalist]: [FurnitureTheme.Modern, FurnitureTheme.Natural, FurnitureTheme.Industrial]
            };

            const compatibleThemes = harmoniousThemes[theme1] || [];
            return compatibleThemes.includes(theme2);
        }

        // 计算特殊组合奖励
        function calculateSpecialCombinationBonus(template1, template2) {
            // 定义特殊组合规则
            const specialCombinations = [
                // 桌椅组合
                { types: [FurnitureType.Medium, FurnitureType.Small], bonus: 3, description: "桌椅组合" },
                // 沙发茶几组合
                { types: [FurnitureType.Large, FurnitureType.Medium], bonus: 2, description: "沙发茶几组合" },
                // 挂饰与家具组合
                { types: [FurnitureType.WallDecoration, FurnitureType.Small], bonus: 2, description: "挂饰装点" },
                { types: [FurnitureType.WallDecoration, FurnitureType.Medium], bonus: 2, description: "挂饰装点" },
                { types: [FurnitureType.WallDecoration, FurnitureType.Large], bonus: 1, description: "挂饰装点" }
            ];

            for (const combination of specialCombinations) {
                if ((combination.types.includes(template1.type) &&
                     combination.types.includes(template2.type)) &&
                    template1.type !== template2.type) {
                    return combination.bonus;
                }
            }

            return 0;
        }

        // 计算尺寸搭配奖励
        function calculateSizeMatchBonus(furniture1, furniture2) {
            const size1 = furniture1.currentSize.x * furniture1.currentSize.y;
            const size2 = furniture2.currentSize.x * furniture2.currentSize.y;

            // 大小家具搭配奖励
            if (Math.abs(size1 - size2) >= 2) {
                return 1; // 大小搭配+1分
            }

            return 0;
        }

        // 创建家具配对的唯一标识符
        function createPairId(id1, id2) {
            // 确保ID顺序一致，避免重复
            return id1 < id2 ? `${id1}_${id2}` : `${id2}_${id1}`;
        }

        // 获取主导主题
        function getDominantTheme(furnitures) {
            if (furnitures.length === 0) return null;

            const themeCount = new Map();
            furnitures.forEach(furniture => {
                const template = furnitureTemplates.find(t => t.id === furniture.templateId);
                if (template) {
                    const theme = template.properties.theme;
                    themeCount.set(theme, (themeCount.get(theme) || 0) + 1);
                }
            });

            let maxCount = 0;
            let dominantTheme = null;
            themeCount.forEach((count, theme) => {
                if (count > maxCount) {
                    maxCount = count;
                    dominantTheme = theme;
                }
            });

            return dominantTheme;
        }

        // 获取主题名称
        function getThemeName(theme) {
            const themeNames = {
                [FurnitureTheme.Modern]: '现代风格',
                [FurnitureTheme.Classic]: '古典风格',
                [FurnitureTheme.Natural]: '自然风格',
                [FurnitureTheme.Industrial]: '工业风格',
                [FurnitureTheme.Minimalist]: '简约风格'
            };
            return themeNames[theme] || '无';
        }

        // 更新评分显示
        function updateScoreDisplay(scoreDetails) {
            document.getElementById('totalScore').textContent = scoreDetails.totalScore;
            document.getElementById('themeScore').textContent = scoreDetails.themeScore;
            document.getElementById('quantityScore').textContent = scoreDetails.quantityScore;
            document.getElementById('valueScore').textContent = scoreDetails.valueScore;
            document.getElementById('layoutScore').textContent = scoreDetails.layoutScore;
            document.getElementById('adjacentScore').textContent = scoreDetails.adjacentScore;
            document.getElementById('dominantTheme').textContent = scoreDetails.dominantTheme;
        }

        // 检查是否可以放置家具
        function canPlaceFurniture(roomConfig, template, position, rotation = Rotation.Deg0) {
            const currentSize = getRotatedSize(template.baseSize, rotation);

            // 检查边界
            if (position.x + currentSize.x > roomConfig.size.x ||
                position.y + currentSize.y > roomConfig.size.y ||
                position.x < 0 || position.y < 0) {
                return false;
            }

            // 检查每个格子
            for (let dx = 0; dx < currentSize.x; dx++) {
                for (let dy = 0; dy < currentSize.y; dy++) {
                    const checkX = position.x + dx;
                    const checkY = position.y + dy;
                    const gridState = roomConfig.layout[checkY][checkX];

                    // 障碍物不能放置
                    if (gridState === GridState.Obstacle) {
                        return false;
                    }

                    // 已占用不能放置
                    if (gridState === GridState.Occupied) {
                        return false;
                    }

                    // 挂饰类家具只能放在邻墙格子上
                    if (template.properties.isWallDecoration &&
                        gridState !== GridState.WallAdjacent1 &&
                        gridState !== GridState.WallAdjacent2) {
                        return false;
                    }
                }
            }

            return true;
        }

        // 更新格子占用状态
        function updateGridOccupancy(roomConfig, furniture, isOccupied) {
            const template = furnitureTemplates.find(t => t.id === furniture.templateId);
            if (!template) return;

            const currentSize = getRotatedSize(template.baseSize, furniture.rotation);

            // 获取原始布局
            const originalLayout = getOriginalLayout(roomConfig.id);

            for (let dx = 0; dx < currentSize.x; dx++) {
                for (let dy = 0; dy < currentSize.y; dy++) {
                    const x = furniture.position.x + dx;
                    const y = furniture.position.y + dy;

                    if (x >= 0 && x < roomConfig.size.x && y >= 0 && y < roomConfig.size.y) {
                        if (isOccupied) {
                            roomConfig.layout[y][x] = GridState.Occupied;
                        } else {
                            // 恢复原始状态
                            roomConfig.layout[y][x] = originalLayout[y][x];
                        }
                    }
                }
            }
        }

        // 获取原始布局
        function getOriginalLayout(roomId) {
            if (roomId === 'roomA') {
                return [
                    [3,3,3,3,3,0,0,3,3,3],
                    [3,3,3,3,3,0,0,0,3,3],
                    [0,0,0,3,3,0,0,0,0,3],
                    [0,0,0,3,3,0,0,0,0,0],
                    [0,0,0,0,0,0,0,0,0,0],
                    [0,0,0,0,0,0,0,0,0,2],
                    [0,0,0,0,0,0,0,0,0,2],
                    [0,0,0,0,0,0,0,0,0,3],
                    [3,0,0,0,0,0,0,0,3,3],
                    [3,3,0,0,0,0,0,3,3,3]
                ];
            } else if (roomId === 'roomB') {
                return [
                    [0,0,0,0,2],
                    [0,0,0,0,2],
                    [0,0,0,0,2],
                    [0,0,0,0,2]
                ];
            }
            return [];
        }

        // 清空所有家具
        function clearAllFurniture() {
            // 重置房间A
            roomConfigs.roomA.furnitures = [];
            roomConfigs.roomA.layout = [
                [3,3,3,3,3,0,0,3,3,3],
                [3,3,3,3,3,0,0,0,3,3],
                [0,0,0,3,3,0,0,0,0,3],
                [0,0,0,3,3,0,0,0,0,0],
                [0,0,0,0,0,0,0,0,0,0],
                [0,0,0,0,0,0,0,0,0,2],
                [0,0,0,0,0,0,0,0,0,2],
                [0,0,0,0,0,0,0,0,0,3],
                [3,0,0,0,0,0,0,0,3,3],
                [3,3,0,0,0,0,0,3,3,3]
            ];

            // 重置房间B
            roomConfigs.roomB.furnitures = [];
            roomConfigs.roomB.layout = [
                [0,0,0,0,2],
                [0,0,0,0,2],
                [0,0,0,0,2],
                [0,0,0,0,2]
            ];

            // 重新渲染所有房间
            renderRoom('roomA', roomConfigs.roomA);
            renderRoom('roomB', roomConfigs.roomB);

            // 重新计算评分
            calculateScore();

            console.log('已清空所有家具');
        }

        // 重置房间
        function resetRooms() {
            clearAllFurniture();
            console.log('已重置所有房间');
        }

        // 设置操作模式
        function setMode(mode) {
            currentMode = mode;
            selectedFurniture = null;

            // 更新按钮状态
            document.querySelectorAll('.mode-btn').forEach(btn => btn.classList.remove('active'));
            document.getElementById(mode + 'Mode').classList.add('active');

            // 重新渲染房间以更新选中状态
            renderRoom('roomA', roomConfigs.roomA);
            renderRoom('roomB', roomConfigs.roomB);

            console.log(`切换到${mode}模式`);
        }

        // 初始化家具选择器
        function initializeFurnitureSelector() {
            const options = document.querySelectorAll('.furniture-option');
            options.forEach(option => {
                option.addEventListener('click', function() {
                    // 移除其他选中状态
                    options.forEach(opt => opt.classList.remove('selected'));
                    // 添加当前选中状态
                    this.classList.add('selected');
                    // 更新选中的模板ID
                    selectedTemplateId = parseInt(this.dataset.templateId);
                    console.log(`选中家具模板: ${selectedTemplateId}`);
                });
            });
        }

        // 处理格子点击
        function handleGridClick(roomConfig, x, y) {
            const position = { x, y };

            switch (currentMode) {
                case 'place':
                    placeFurnitureAtPosition(roomConfig, position);
                    break;
                case 'remove':
                    removeFurnitureAtPosition(roomConfig, position);
                    break;
                case 'rotate':
                    rotateFurnitureAtPosition(roomConfig, position);
                    break;
            }
        }

        // 在指定位置放置家具
        function placeFurnitureAtPosition(roomConfig, position) {
            const template = furnitureTemplates.find(t => t.id === selectedTemplateId);
            if (!template) {
                console.log('未找到家具模板');
                return;
            }

            // 检查是否可以放置
            if (!canPlaceFurniture(roomConfig, template, position, Rotation.Deg0)) {
                console.log(`无法在位置 (${position.x}, ${position.y}) 放置 ${template.name}`);
                return;
            }

            // 创建家具实例
            const furniture = {
                id: generateFurnitureId(),
                templateId: template.id,
                position: { x: position.x, y: position.y },
                rotation: Rotation.Deg0,
                currentSize: getRotatedSize(template.baseSize, Rotation.Deg0),
                placedTime: Date.now()
            };

            // 添加家具到房间
            roomConfig.furnitures.push(furniture);

            // 更新格子状态
            updateGridOccupancy(roomConfig, furniture, true);

            // 重新渲染房间
            const roomId = roomConfig.id;
            renderRoom(roomId, roomConfig);

            // 重新计算评分
            calculateScore();

            console.log(`成功在 (${position.x}, ${position.y}) 放置 ${template.name}`);
        }

        // 移除指定位置的家具
        function removeFurnitureAtPosition(roomConfig, position) {
            // 查找该位置的家具
            const furniture = roomConfig.furnitures.find(f => {
                const currentSize = f.currentSize;
                return position.x >= f.position.x &&
                       position.x < f.position.x + currentSize.x &&
                       position.y >= f.position.y &&
                       position.y < f.position.y + currentSize.y;
            });

            if (!furniture) {
                console.log(`位置 (${position.x}, ${position.y}) 没有家具`);
                return;
            }

            // 移除家具
            const index = roomConfig.furnitures.indexOf(furniture);
            roomConfig.furnitures.splice(index, 1);

            // 更新格子状态
            updateGridOccupancy(roomConfig, furniture, false);

            // 重新渲染房间
            const roomId = roomConfig.id;
            renderRoom(roomId, roomConfig);

            // 重新计算评分
            calculateScore();

            const template = furnitureTemplates.find(t => t.id === furniture.templateId);
            console.log(`移除了 ${template ? template.name : '家具'}`);
        }

        // 旋转指定位置的家具
        function rotateFurnitureAtPosition(roomConfig, position) {
            // 查找该位置的家具
            const furniture = roomConfig.furnitures.find(f => {
                const currentSize = f.currentSize;
                return position.x >= f.position.x &&
                       position.x < f.position.x + currentSize.x &&
                       position.y >= f.position.y &&
                       position.y < f.position.y + currentSize.y;
            });

            if (!furniture) {
                console.log(`位置 (${position.x}, ${position.y}) 没有家具`);
                return;
            }

            // 调用旋转函数
            rotateFurniture(furniture.id, roomConfig);
        }
    </script>
</body>
</html>
