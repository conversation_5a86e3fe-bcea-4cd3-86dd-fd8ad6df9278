import { _decorator, Component, Enum, Node, Vec2, UIOpacity } from 'cc';

const { ccclass, property } = _decorator;
const DecorationEnum = Enum({
    Type0: 0,//可以摆放家具
    Type1: 1,//不可以摆放家具
});

@ccclass('GridComp')
export class GridComp extends Component {

    @property({ type: Enum(DecorationEnum), visible: false })
    private _decorationType = DecorationEnum.Type0;
    //枚举
    @property({ type: Enum(DecorationEnum), tooltip: '设置格子的摆放属性, 当decorationType == Type3时, 该格子节点自动不可见' })
    get decorationType() {
        return this._decorationType;
    };

    set decorationType(value) {
        this._decorationType = value;
        if (value === DecorationEnum.Type0) {
            this.node.active = false;
        }
        else {
            this.node.active = true;
        }
    };

    @property({ displayName: "", tooltip: "房间的格子序号, 左上角" })
    public indexVec2: Vec2 = new Vec2();

    start() {

    }

    update(deltaTime: number) {

    }
}


