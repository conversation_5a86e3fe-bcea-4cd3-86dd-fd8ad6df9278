import { _decorator, Component, Enum, Node, Vec2, UIOpacity } from 'cc';

const { ccclass, property } = _decorator;
const DecorationEnum = Enum({
    Type0: 0,//可以摆放除了挂饰以外的家具,中间空地, 或贴近窗口的位置(窗口虽然属于"墙", 但是不允许在窗子放挂饰)
    Type1: 1,//可以摆放家具或向右摆放挂饰,左侧邻墙位置, 不包含地图最顶部的格子, 挂饰拖拽到这个格子时会自动调整为朝向右
    Type2: 2,//可以摆放家具或向左摆放挂饰,右侧邻墙位置, 包含地图最顶部的格子(左右两面墙交汇处的格子, 划分给左侧邻墙管理), 挂饰拖拽到这个格子时会自动调整为朝向左
    Type3: 3,//不可以摆放任何家具

});

@ccclass('GridComp')
export class GridComp extends Component {

    @property({ type: Enum(DecorationEnum), visible: false })
    private _decorationType = DecorationEnum.Type3;
    //枚举
    @property({ type: Enum(DecorationEnum), tooltip: '设置格子的摆放属性, 当decorationType == Type3时, 该格子节点自动不可见' })
    get decorationType() {
        return this._decorationType;
    };

    set decorationType(value) {
        this._decorationType = value;
        if (value === DecorationEnum.Type0) {
            const uiOpacity = this.node.getComponent(UIOpacity);
            if (uiOpacity) {
                uiOpacity.opacity = 0;
            }
        }
        else {
            const uiOpacity = this.node.getComponent(UIOpacity);
            if (uiOpacity) {
                uiOpacity.opacity = 255;
            }
        }
    };

    @property({ displayName: "", tooltip: "房间的格子序号, 左上角" })
    public indexVec2: Vec2 = new Vec2();

    start() {

    }

    update(deltaTime: number) {

    }
}


