格子分类: 地面格子, 地面邻墙格子(接近墙壁的地面格子), 墙壁格子, 墙壁邻地格子(接近地面的墙壁格子), 障碍格子

家具主要分类: 普通家具(只可以摆放在地面), 贴墙类家具(只能摆放在地面邻墙格子, 例如木门, 飘台, 灶台, 壁炉), 普通挂饰类家具(只能摆放在墙壁), 低空类挂饰(只能摆放在墙壁邻地格子, 例如电视墙)

使用四个空间系统, 四个房间分别命名为 房间A, 房间B, 墙壁C, 墙壁D
以下用数字表示格子属性: 
数字0是地面格子(只能摆放普通家具), 
数字1、数字2是地面邻墙格子(可以摆放普通家具和贴墙类的家具),  
数字3、数字4是墙壁格子(只能摆放普通挂饰), 
数字5、数字6是墙壁邻地格子(可以摆放普通挂饰和低空类挂饰), 
数字9是障碍(不能摆放任何物件);

用二维数组表示房间A的格子分布
从上到下表示房间A的每一行格子
[
[9, 9, 9, 9, 9, 1, 1, 9, 9, 9],
[9, 9, 9, 9, 9, 0, 0, 0, 9, 9],
[0, 0, 0, 9, 9, 0, 0, 0, 0, 9],
[0, 0, 0, 9, 9, 0, 0, 0, 0, 2],
[0, 0, 0, 0, 0, 0, 0, 0, 0, 2],
[0, 0, 0, 0, 0, 0, 0, 0, 0, 2],
[0, 0, 0, 0, 0, 0, 0, 0, 0, 2],
[0, 0, 0, 0, 0, 0, 0, 0, 0, 9],
[9, 0, 0, 0, 0, 0, 0, 0, 9, 9],
[9, 9, 0, 0, 0, 0, 0, 9, 9, 9]
]


用二维数组表示房间B的格子分布
从上到下表示房间B的每一行格子
[
  [1, 1, 1, 1, 2],
  [0, 0, 0, 0, 2],
  [0, 0, 0, 0, 2],
  [0, 0, 0, 0, 2]
]

用二维数组表示墙壁C的格子分布
从上到下表示墙壁D的每一行格子
[
  [3, 3, 3, 3, 3, 3, 3],
  [3, 3, 9, 9, 3, 3, 3],
  [3, 3, 9, 9, 3, 3, 3],
  [3, 3, 3, 3, 3, 3, 3],
  [3, 9, 9, 9, 9, 9, 3],
  [3, 9, 9, 9, 9, 9, 9],
  [3, 9, 9, 9, 9, 9, 9],
  [9, 9, 9, 9, 9, 9, 9],
  [9, 9, 9, 9, 9, 9, 9],
  [9, 9, 9, 9, 9, 5, 5],
  [5, 9, 9, 9, 9, 9, 5],
]

用二维数组表示墙壁D的格子分布
从上到下表示墙壁D的每一行格子
[
 [4, 4, 4, 4, 4, 4, 4],
 [4, 4, 4, 4, 4, 4, 4],
 [4, 4, 4, 4, 4, 4, 4],
 [4, 4, 4, 4, 4, 4, 4],
 [4, 4, 4, 4, 4, 4, 4],
 [9, 9, 9, 9, 4, 4, 4],
 [9, 9, 9, 4, 4, 4, 4],
 [9, 9, 4, 9, 9, 9, 4],
 [9, 4, 4, 9, 9, 9, 4],
 [6, 6, 6, 9, 9, 9, 6],
 [6, 9, 9, 9, 9, 9, 9],
]

额外要求:
1 不能跨两个房间摆放家具
2 不能跨两个墙壁摆放家具
3 不能跨房间与墙壁摆放家具
4 所有房间和所有墙壁都共用一个评分系统