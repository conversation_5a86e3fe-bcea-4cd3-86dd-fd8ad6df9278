使用四房间系统, 四个房间分别命名为 房间A , 房间B, 房间C , 房间D
以下用数字表示格子属性: 数字0是空地(可摆放), 数字1或2是邻墙格子(只可以摆放挂件), 数字3是障碍(不可摆放);

用二维数组表示房间A的格子分布
从上到下表示房间的每一行格子
[
[3, 3, 3, 3, 3, 0, 0, 3, 3, 3],
[3, 3, 3, 3, 3, 0, 0, 0, 3, 3],
[0, 0, 0, 3, 3, 0, 0, 0, 0, 3],
[0, 0, 0, 3, 3, 0, 0, 0, 0, 0],
[0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
[0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
[0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
[0, 0, 0, 0, 0, 0, 0, 0, 0, 3],
[3, 0, 0, 0, 0, 0, 0, 0, 3, 3],
[3, 3, 0, 0, 0, 0, 0, 3, 3, 3]
]


用二维数组表示房间B的格子分布
从上到下表示房间的每一行格子
[
  [0, 0, 0, 0, 0],
  [0, 0, 0, 0, 0],
  [0, 0, 0, 0, 0],
  [0, 0, 0, 0, 0]
]

用二维数组表示房间C的格子分布
从上到下表示房间的每一行格子
[
  [1, 1, 1, 1, 1],
  [1, 1, 1, 1, 1],
  [1, 1, 1, 1, 1],
  [1, 1, 1, 1, 1]
]

用二维数组表示房间D的格子分布
从上到下表示房间的每一行格子
[
  [2, 2, 2, 2, 2],
  [2, 2, 2, 2, 2],
  [2, 2, 2, 2, 2],
  [2, 2, 2, 2, 2]
]

要求:
所有房间不相邻, 不能跨房间摆放家具
所有房间共用一个评分系统