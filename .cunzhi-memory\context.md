# 项目上下文信息

- dig_deeper是一个基于Cocos Creator 3.8.5的挖矿类游戏项目，主要特点：1.使用TypeScript开发，支持微信小游戏平台；2.核心系统包括挖矿系统、卡牌系统、装备系统、任务系统等；3.采用模块化架构，各系统通过事件系统通信；4.支持热力图寻路、流场算法等高级功能；5.包含完整的构建和部署脚本
- 修复了GridComp.ts中UIOpacity组件获取的类型兼容性问题，并更新了房间格子属性值系统：0=空地，1/2=邻墙格子(挂饰可用)，3=障碍，同时更新了所有相关验证逻辑
- 更新了HTML测试页面room-placement-test.html，包括GridState枚举、房间布局数据、所有相关验证和显示函数，以匹配新的格子属性系统(0=空地,1/2=邻墙,3=障碍)
- 在HTML测试页面中添加了完整的相邻评分功能，包括评分显示面板、计算逻辑、奖励规则(同类型+4分、同主题+3分、和谐搭配+2分、特殊组合等)，权重10%，最高100分
- 成功恢复了挂饰类家具系统的所有功能，包括FurnitureType.WallDecoration枚举、isWallDecoration属性、家具模板数据(现代壁画、古典挂钟)、验证逻辑、评分系统和测试页面
- 创建了四房间摆放系统测试页面，实现了新的摆放规则：普通家具只能放在格子0(空地)，挂饰类家具只能放在格子1/2(邻墙)，四个房间不相邻但共用评分系统
- 在家具模板中创建了多尺寸挂饰数据：1×1(现代壁画、古典挂钟)、2×1(工业风金属装饰、简约艺术挂件)、2×2(自然风植物壁挂、现代抽象画组)，所有挂饰都只能摆放在格子1/2上
- 在测试页面添加了拖动模式，支持拖拽已摆放的家具到新位置，包含实时验证、视觉反馈(蓝色可放置/红色不可放置)、跨房间移动、自动评分更新等功能
- 修复了测试页面拖拽功能的问题：通过临时隐藏拖拽元素来正确检测鼠标下方的格子，添加pointerEvents控制，增强数据验证和调试日志，现在拖拽释放后可以正确放置到新位置
