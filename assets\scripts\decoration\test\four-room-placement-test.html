<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>四房间摆放系统测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }

        .header h1 {
            color: #333;
            margin-bottom: 10px;
        }

        .rooms-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .room-section {
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            background: #f8f9fa;
        }

        .room-title {
            font-weight: bold;
            margin-bottom: 10px;
            text-align: center;
            color: #495057;
            font-size: 16px;
        }

        .room-grid {
            display: inline-block;
            border: 2px solid #333;
            background: white;
            margin: 0 auto;
        }

        .grid-row {
            display: flex;
        }

        .grid-cell {
            width: 30px;
            height: 30px;
            border: 1px solid #ccc;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            position: relative;
            font-size: 10px;
            font-weight: bold;
        }

        .grid-empty {
            background-color: #e8f5e8;
            color: #2d5a2d;
        }

        .grid-wall-adjacent {
            background-color: #e8e8f5;
            color: #2d2d5a;
        }

        .grid-obstacle {
            background-color: #f5e8e8;
            color: #5a2d2d;
        }

        .grid-occupied {
            background-color: #fff3cd;
            color: #856404;
        }

        .grid-cell:hover {
            opacity: 0.8;
            transform: scale(1.1);
        }

        .grid-cell.highlight {
            box-shadow: 0 0 0 2px #007bff;
        }

        .furniture {
            position: absolute;
            background: #007bff;
            color: white;
            border: 2px solid #0056b3;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            cursor: pointer;
            z-index: 10;
        }

        .furniture.wall-decoration {
            background: #6f42c1;
            border-color: #5a2d91;
        }

        .furniture.selected {
            box-shadow: 0 0 0 3px #ffc107;
        }

        .furniture:hover {
            opacity: 0.9;
            transform: scale(1.05);
        }

        .furniture.dragging {
            opacity: 0.7;
            z-index: 1000;
            pointer-events: none;
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }

        .grid-cell.drag-over {
            background-color: rgba(0, 123, 255, 0.3) !important;
            border: 2px solid #007bff !important;
        }

        .grid-cell.drag-invalid {
            background-color: rgba(220, 53, 69, 0.3) !important;
            border: 2px solid #dc3545 !important;
        }

        .mode-selector {
            text-align: center;
            margin-bottom: 20px;
        }

        .mode-btn {
            padding: 8px 16px;
            margin: 0 5px;
            border: 2px solid #007bff;
            background: white;
            color: #007bff;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }

        .mode-btn.active {
            background: #007bff;
            color: white;
        }

        .furniture-selector {
            margin-bottom: 20px;
            text-align: center;
        }

        .furniture-options {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px;
            margin-top: 10px;
        }

        .furniture-option {
            padding: 8px 12px;
            border: 2px solid #6c757d;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .furniture-option.selected {
            background: #007bff;
            color: white;
            border-color: #0056b3;
        }

        .furniture-option:hover {
            background: #e9ecef;
        }

        .furniture-option.selected:hover {
            background: #0056b3;
        }

        .score-panel {
            background: #e9ecef;
            border: 1px solid #ced4da;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }

        .score-title {
            font-weight: bold;
            color: #495057;
            margin-bottom: 10px;
            font-size: 16px;
        }

        .score-details {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            gap: 10px;
        }

        .score-item {
            background: white;
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            min-width: 80px;
        }

        .score-label {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 2px;
        }

        .score-value {
            font-size: 16px;
            font-weight: bold;
            color: #495057;
        }

        .total-score {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .total-score .score-label {
            color: #cce7ff;
        }

        .total-score .score-value {
            color: white;
            font-size: 18px;
        }

        .info-panel {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
            height: 180px;
            overflow-y: auto;
        }

        .info-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #495057;
        }

        .legend {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .legend-color {
            width: 20px;
            height: 20px;
            border: 1px solid #333;
            border-radius: 2px;
        }

        .controls {
            text-align: center;
            margin-top: 20px;
        }

        .btn {
            padding: 10px 20px;
            margin: 0 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            font-size: 14px;
        }

        .btn.success {
            background: #28a745;
            color: white;
        }

        .btn.secondary {
            background: #6c757d;
            color: white;
        }

        .btn:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }

        .btn.success:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>四房间摆放系统测试</h1>
            <p>选择操作模式：摆放模式点击格子放置家具，移除/旋转模式直接点击家具操作，拖拽模式按住家具拖拽移动</p>
            <p style="color: #6c757d; font-size: 14px;">注意：四个房间不相邻，不能跨房间摆放家具，共用一个评分系统</p>
        </div>

        <div class="mode-selector">
            <span style="margin-right: 15px; font-weight: bold;">操作模式:</span>
            <button class="mode-btn active" onclick="setMode('place')" id="placeMode">摆放模式</button>
            <button class="mode-btn" onclick="setMode('remove')" id="removeMode">移除模式</button>
            <button class="mode-btn" onclick="setMode('rotate')" id="rotateMode">旋转模式</button>
            <button class="mode-btn" onclick="setMode('drag')" id="dragMode">拖拽模式</button>
        </div>

        <div class="furniture-selector">
            <div style="font-weight: bold; margin-bottom: 10px;">选择家具类型:</div>
            <div class="furniture-options">
                <div class="furniture-option selected" data-template-id="1">现代椅子 (1×1)</div>
                <div class="furniture-option" data-template-id="2">古典床 (2×1)</div>
                <div class="furniture-option" data-template-id="3">工业风桌子 (2×2)</div>
                <div class="furniture-option" data-template-id="4">简约台灯 (1×1)</div>
                <div class="furniture-option" data-template-id="5">自然风沙发 (2×1)</div>
                <div class="furniture-option" data-template-id="6">古典书柜 (2×2)</div>
                <div class="furniture-option" data-template-id="11">现代壁画 (1×1 挂饰)</div>
                <div class="furniture-option" data-template-id="12">古典挂钟 (1×1 挂饰)</div>
                <div class="furniture-option" data-template-id="13">工业风金属装饰 (2×1 挂饰)</div>
                <div class="furniture-option" data-template-id="14">简约艺术挂件 (2×1 挂饰)</div>
                <div class="furniture-option" data-template-id="15">自然风植物壁挂 (2×2 挂饰)</div>
                <div class="furniture-option" data-template-id="16">现代抽象画组 (2×2 挂饰)</div>
            </div>
        </div>

        <div class="score-panel">
            <div class="score-title">四房间装饰评分</div>
            <div class="score-details" id="scoreDetails">
                <div class="score-item total-score">
                    <div class="score-label">总评分</div>
                    <div class="score-value" id="totalScore">0</div>
                </div>
                <div class="score-item">
                    <div class="score-label">主题评分</div>
                    <div class="score-value" id="themeScore">0</div>
                </div>
                <div class="score-item">
                    <div class="score-label">数量评分</div>
                    <div class="score-value" id="quantityScore">0</div>
                </div>
                <div class="score-item">
                    <div class="score-label">价值评分</div>
                    <div class="score-value" id="valueScore">0</div>
                </div>
                <div class="score-item">
                    <div class="score-label">布局评分</div>
                    <div class="score-value" id="layoutScore">0</div>
                </div>
                <div class="score-item">
                    <div class="score-label">相邻评分</div>
                    <div class="score-value" id="adjacentScore">0</div>
                </div>
                <div class="score-item">
                    <div class="score-label">主导主题</div>
                    <div class="score-value" id="dominantTheme">无</div>
                </div>
            </div>
        </div>
        
        <div class="info-panel">
            <div class="info-title">信息面板</div>
            <div id="infoContent">将鼠标悬停在房间格子或家具上查看详细信息...</div>
        </div>
        
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color grid-empty"></div>
                <span>空地 (0) - 普通家具</span>
            </div>
            <div class="legend-item">
                <div class="legend-color grid-wall-adjacent"></div>
                <span>邻墙 (1/2) - 挂饰专用</span>
            </div>
            <div class="legend-item">
                <div class="legend-color grid-obstacle"></div>
                <span>障碍 (3)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color grid-occupied"></div>
                <span>占用 (4)</span>
            </div>
        </div>

        <div class="rooms-container">
            <div class="room-section">
                <div class="room-title">房间A (10×10)</div>
                <div class="room-grid" id="roomA"></div>
            </div>
            <div class="room-section">
                <div class="room-title">房间B (5×4)</div>
                <div class="room-grid" id="roomB"></div>
            </div>
            <div class="room-section">
                <div class="room-title">房间C (5×4) - 挂饰专用</div>
                <div class="room-grid" id="roomC"></div>
            </div>
            <div class="room-section">
                <div class="room-title">房间D (5×4) - 挂饰专用</div>
                <div class="room-grid" id="roomD"></div>
            </div>
        </div>
        
        <div class="controls">
            <button class="btn success" onclick="calculateScore()">计算评分</button>
            <button class="btn secondary" onclick="clearAllFurniture()">清空家具</button>
            <button class="btn secondary" onclick="resetRooms()">重置房间</button>
        </div>
    </div>

    <script>
        // 四房间摆放系统测试脚本

        // 枚举定义
        const GridState = {
            Empty: 0,          // 空地（只能摆放普通家具）
            WallAdjacent1: 1,  // 邻墙格子1（只能摆放挂饰类家具）
            WallAdjacent2: 2,  // 邻墙格子2（只能摆放挂饰类家具）
            Obstacle: 3,       // 障碍
            Occupied: 4        // 占用
        };

        const FurnitureType = {
            Small: 1,           // 1×1 小型家具
            Medium: 2,          // 2×1 中型家具
            Large: 3,           // 2×2 大型家具
            WallDecoration: 4   // 挂饰类家具
        };

        const FurnitureTheme = {
            Modern: 1,      // 现代风格
            Classic: 2,     // 古典风格
            Natural: 3,     // 自然风格
            Industrial: 4,  // 工业风格
            Minimalist: 5   // 简约风格
        };

        const Rotation = {
            Deg0: 0,
            Deg90: 90
        };

        // 四房间配置（四个房间不相邻，独立布局）
        const roomConfigs = {
            roomA: {
                id: 'roomA',
                name: '房间A',
                size: { x: 10, y: 10 },
                layout: [
                    [3, 3, 3, 3, 3, 0, 0, 3, 3, 3],
                    [3, 3, 3, 3, 3, 0, 0, 0, 3, 3],
                    [0, 0, 0, 3, 3, 0, 0, 0, 0, 3],
                    [0, 0, 0, 3, 3, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0, 3],
                    [3, 0, 0, 0, 0, 0, 0, 0, 3, 3],
                    [3, 3, 0, 0, 0, 0, 0, 3, 3, 3]
                ],
                furnitures: []
            },
            roomB: {
                id: 'roomB',
                name: '房间B',
                size: { x: 5, y: 4 },
                layout: [
                    [0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0]
                ],
                furnitures: []
            },
            roomC: {
                id: 'roomC',
                name: '房间C',
                size: { x: 5, y: 4 },
                layout: [
                    [1, 1, 1, 1, 1],
                    [1, 1, 1, 1, 1],
                    [1, 1, 1, 1, 1],
                    [1, 1, 1, 1, 1]
                ],
                furnitures: []
            },
            roomD: {
                id: 'roomD',
                name: '房间D',
                size: { x: 5, y: 4 },
                layout: [
                    [2, 2, 2, 2, 2],
                    [2, 2, 2, 2, 2],
                    [2, 2, 2, 2, 2],
                    [2, 2, 2, 2, 2]
                ],
                furnitures: []
            }
        };

        // 家具模板
        const furnitureTemplates = [
            {
                id: 1,
                name: '现代椅子',
                type: FurnitureType.Small,
                baseSize: { x: 1, y: 1 },
                properties: {
                    theme: FurnitureTheme.Modern,
                    level: 1,
                    value: 8,
                    beauty: 6,
                    isWallDecoration: false
                }
            },
            {
                id: 2,
                name: '古典床',
                type: FurnitureType.Medium,
                baseSize: { x: 2, y: 1 },
                properties: {
                    theme: FurnitureTheme.Classic,
                    level: 2,
                    value: 15,
                    beauty: 8,
                    isWallDecoration: false
                }
            },
            {
                id: 3,
                name: '工业风桌子',
                type: FurnitureType.Large,
                baseSize: { x: 2, y: 2 },
                properties: {
                    theme: FurnitureTheme.Industrial,
                    level: 2,
                    value: 12,
                    beauty: 5,
                    isWallDecoration: false
                }
            },
            {
                id: 4,
                name: '简约台灯',
                type: FurnitureType.Small,
                baseSize: { x: 1, y: 1 },
                properties: {
                    theme: FurnitureTheme.Minimalist,
                    level: 1,
                    value: 6,
                    beauty: 7,
                    isWallDecoration: false
                }
            },
            {
                id: 5,
                name: '自然风沙发',
                type: FurnitureType.Medium,
                baseSize: { x: 2, y: 1 },
                properties: {
                    theme: FurnitureTheme.Natural,
                    level: 3,
                    value: 20,
                    beauty: 9,
                    isWallDecoration: false
                }
            },
            {
                id: 6,
                name: '古典书柜',
                type: FurnitureType.Large,
                baseSize: { x: 2, y: 2 },
                properties: {
                    theme: FurnitureTheme.Classic,
                    level: 3,
                    value: 25,
                    beauty: 10,
                    isWallDecoration: false
                }
            },
            {
                id: 11,
                name: '现代壁画',
                type: FurnitureType.WallDecoration,
                baseSize: { x: 1, y: 1 },
                properties: {
                    theme: FurnitureTheme.Modern,
                    level: 2,
                    value: 12,
                    beauty: 10,
                    isWallDecoration: true
                }
            },
            {
                id: 12,
                name: '古典挂钟',
                type: FurnitureType.WallDecoration,
                baseSize: { x: 1, y: 1 },
                properties: {
                    theme: FurnitureTheme.Classic,
                    level: 2,
                    value: 10,
                    beauty: 8,
                    isWallDecoration: true
                }
            },
            {
                id: 13,
                name: '工业风金属装饰',
                type: FurnitureType.WallDecoration,
                baseSize: { x: 2, y: 1 },
                properties: {
                    theme: FurnitureTheme.Industrial,
                    level: 3,
                    value: 18,
                    beauty: 12,
                    isWallDecoration: true
                }
            },
            {
                id: 14,
                name: '简约艺术挂件',
                type: FurnitureType.WallDecoration,
                baseSize: { x: 2, y: 1 },
                properties: {
                    theme: FurnitureTheme.Minimalist,
                    level: 3,
                    value: 20,
                    beauty: 14,
                    isWallDecoration: true
                }
            },
            {
                id: 15,
                name: '自然风植物壁挂',
                type: FurnitureType.WallDecoration,
                baseSize: { x: 2, y: 2 },
                properties: {
                    theme: FurnitureTheme.Natural,
                    level: 4,
                    value: 35,
                    beauty: 20,
                    isWallDecoration: true
                }
            },
            {
                id: 16,
                name: '现代抽象画组',
                type: FurnitureType.WallDecoration,
                baseSize: { x: 2, y: 2 },
                properties: {
                    theme: FurnitureTheme.Modern,
                    level: 4,
                    value: 40,
                    beauty: 22,
                    isWallDecoration: true
                }
            }
        ];

        // 全局变量
        let infoContent = null;
        let selectedFurniture = null; // 当前选中的家具
        let currentMode = 'place'; // 当前操作模式：place, remove, rotate, drag
        let selectedTemplateId = 1; // 当前选中的家具模板ID

        // 拖拽相关变量
        let draggedFurniture = null; // 正在拖拽的家具
        let draggedFurnitureRoom = null; // 被拖拽家具所在的房间
        let dragOffset = { x: 0, y: 0 }; // 拖拽偏移量
        let isDragging = false; // 是否正在拖拽

        // 评分权重配置
        const scoreWeights = {
            theme: 0.4,      // 主题权重
            quantity: 0.2,   // 数量权重
            value: 0.25,     // 价值权重
            layout: 0.25,    // 布局权重
            adjacent: 0.1    // 相邻加成权重
        };

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            infoContent = document.getElementById('infoContent');
            initializeRooms();
            calculateScore(); // 初始计算评分
            initializeFurnitureSelector();
        });

        // 初始化房间
        function initializeRooms() {
            Object.keys(roomConfigs).forEach(roomId => {
                renderRoom(roomId, roomConfigs[roomId]);
            });
        }

        // 渲染房间
        function renderRoom(roomId, roomConfig) {
            const roomElement = document.getElementById(roomId);
            roomElement.innerHTML = '';
            roomElement.style.position = 'relative';

            const { size, layout } = roomConfig;

            // 创建格子
            for (let y = 0; y < size.y; y++) {
                const row = document.createElement('div');
                row.className = 'grid-row';

                for (let x = 0; x < size.x; x++) {
                    const cell = document.createElement('div');
                    cell.className = 'grid-cell';
                    cell.dataset.x = x;
                    cell.dataset.y = y;
                    cell.dataset.roomId = roomId;

                    // 设置格子状态样式
                    const gridState = layout[y][x];
                    switch (gridState) {
                        case GridState.Empty:
                            cell.classList.add('grid-empty');
                            break;
                        case GridState.WallAdjacent1:
                        case GridState.WallAdjacent2:
                            cell.classList.add('grid-wall-adjacent');
                            break;
                        case GridState.Obstacle:
                            cell.classList.add('grid-obstacle');
                            break;
                        case GridState.Occupied:
                            cell.classList.add('grid-occupied');
                            break;
                    }

                    // 添加鼠标事件
                    cell.addEventListener('mouseenter', function() {
                        showGridInfo(roomConfig, x, y, gridState);
                        if (currentMode === 'place') {
                            cell.classList.add('highlight');
                        }
                    });

                    cell.addEventListener('mouseleave', function() {
                        cell.classList.remove('highlight');
                    });

                    // 添加点击事件
                    cell.addEventListener('click', function() {
                        if (currentMode === 'place') {
                            placeFurnitureAt(roomConfig, { x, y });
                        }
                    });

                    row.appendChild(cell);
                }
                roomElement.appendChild(row);
            }

            // 渲染家具
            roomConfig.furnitures.forEach(furniture => {
                const template = furnitureTemplates.find(t => t.id === furniture.templateId);
                if (!template) return;

                const furnitureElement = document.createElement('div');
                furnitureElement.className = 'furniture';
                furnitureElement.dataset.furnitureId = furniture.id;

                if (template.properties.isWallDecoration) {
                    furnitureElement.classList.add('wall-decoration');
                }

                if (selectedFurniture === furniture.id) {
                    furnitureElement.classList.add('selected');
                }

                // 设置位置和尺寸
                const cellSize = 30;
                furnitureElement.style.left = (furniture.position.x * (cellSize + 1)) + 'px';
                furnitureElement.style.top = (furniture.position.y * (cellSize + 1)) + 'px';
                furnitureElement.style.width = (furniture.currentSize.x * (cellSize + 1) - 1) + 'px';
                furnitureElement.style.height = (furniture.currentSize.y * (cellSize + 1) - 1) + 'px';

                furnitureElement.textContent = template.name.substring(0, 2);

                // 添加鼠标事件
                furnitureElement.addEventListener('mouseenter', function() {
                    showFurnitureInfo(roomConfig, furniture, template);
                });

                furnitureElement.addEventListener('click', function(e) {
                    e.stopPropagation();
                    if (currentMode === 'remove') {
                        removeFurniture(roomConfig, furniture.id);
                    } else if (currentMode === 'rotate') {
                        rotateFurniture(roomConfig, furniture.id);
                    } else if (currentMode === 'drag') {
                        // 拖拽模式下不处理点击
                        return;
                    } else {
                        // 选中家具
                        selectedFurniture = selectedFurniture === furniture.id ? null : furniture.id;
                        renderRoom(roomId, roomConfig);
                    }
                });

                // 添加拖拽事件
                if (currentMode === 'drag') {
                    furnitureElement.style.cursor = 'move';
                    furnitureElement.draggable = false; // 禁用默认拖拽

                    furnitureElement.addEventListener('mousedown', function(e) {
                        startDrag(e, furniture, roomConfig);
                    });
                }

                roomElement.appendChild(furnitureElement);
            });
        }

        // 显示格子信息
        function showGridInfo(roomConfig, x, y, gridState) {
            const stateNames = {
                [GridState.Empty]: '空地(普通家具)',
                [GridState.WallAdjacent1]: '邻墙格子1(挂饰专用)',
                [GridState.WallAdjacent2]: '邻墙格子2(挂饰专用)',
                [GridState.Obstacle]: '障碍物',
                [GridState.Occupied]: '已占用'
            };

            const stateName = stateNames[gridState] || '未知';
            const stateColor = getGridStateColor(gridState);

            infoContent.innerHTML = `
                <div><strong>格子信息</strong></div>
                <div>房间: ${roomConfig.name}</div>
                <div>位置: (${x}, ${y})</div>
                <div>状态: <span style="color: ${stateColor}; font-weight: bold;">${stateName} (${gridState})</span></div>
                <div>描述: ${getGridStateDescription(gridState)}</div>
            `;
        }

        // 显示家具信息
        function showFurnitureInfo(roomConfig, furniture, template) {
            infoContent.innerHTML = `
                <div><strong>家具信息</strong></div>
                <div>房间: ${roomConfig.name}</div>
                <div>名称: ${template.name}</div>
                <div>位置: (${furniture.position.x}, ${furniture.position.y})</div>
                <div>尺寸: ${furniture.currentSize.x}×${furniture.currentSize.y}</div>
                <div>旋转: ${furniture.rotation}°</div>
                <div>主题: ${getThemeName(template.properties.theme)}</div>
                <div>等级: ${template.properties.level}</div>
                <div>价值: ${template.properties.value}</div>
                <div>美观度: ${template.properties.beauty}</div>
                <div>挂饰: ${template.properties.isWallDecoration ? '是' : '否'}</div>
                <div>放置时间: ${new Date(furniture.placedTime).toLocaleTimeString()}</div>
            `;
        }

        // 清除信息显示
        function clearInfo() {
            infoContent.innerHTML = '将鼠标悬停在房间格子或家具上查看详细信息...';
        }

        // 获取格子状态颜色
        function getGridStateColor(gridState) {
            switch (gridState) {
                case GridState.Empty: return '#28a745';
                case GridState.WallAdjacent1: return '#6c757d';
                case GridState.WallAdjacent2: return '#6c757d';
                case GridState.Obstacle: return '#dc3545';
                case GridState.Occupied: return '#007bff';
                default: return '#6c757d';
            }
        }

        // 获取格子状态描述
        function getGridStateDescription(gridState) {
            switch (gridState) {
                case GridState.Empty: return '只能放置普通家具';
                case GridState.WallAdjacent1: return '只能放置挂饰类家具';
                case GridState.WallAdjacent2: return '只能放置挂饰类家具';
                case GridState.Obstacle: return '不可放置任何家具';
                case GridState.Occupied: return '已被家具占用';
                default: return '未知状态';
            }
        }

        // 设置操作模式
        function setMode(mode) {
            currentMode = mode;
            selectedFurniture = null;

            // 更新按钮状态
            document.querySelectorAll('.mode-btn').forEach(btn => btn.classList.remove('active'));
            document.getElementById(mode + 'Mode').classList.add('active');

            // 重新渲染所有房间
            Object.keys(roomConfigs).forEach(roomId => {
                renderRoom(roomId, roomConfigs[roomId]);
            });

            clearInfo();
        }

        // 初始化家具选择器
        function initializeFurnitureSelector() {
            document.querySelectorAll('.furniture-option').forEach(option => {
                option.addEventListener('click', function() {
                    // 移除其他选中状态
                    document.querySelectorAll('.furniture-option').forEach(opt => opt.classList.remove('selected'));

                    // 设置当前选中
                    this.classList.add('selected');
                    selectedTemplateId = parseInt(this.dataset.templateId);
                });
            });
        }

        // 检查是否可以放置家具
        function canPlaceFurniture(roomConfig, template, position, rotation = Rotation.Deg0) {
            const currentSize = getRotatedSize(template.baseSize, rotation);

            // 检查是否超出边界
            if (position.x + currentSize.x > roomConfig.size.x ||
                position.y + currentSize.y > roomConfig.size.y) {
                return false;
            }

            // 检查每个格子
            for (let dx = 0; dx < currentSize.x; dx++) {
                for (let dy = 0; dy < currentSize.y; dy++) {
                    const checkX = position.x + dx;
                    const checkY = position.y + dy;
                    const gridState = roomConfig.layout[checkY][checkX];

                    // 障碍物不能放置
                    if (gridState === GridState.Obstacle) {
                        return false;
                    }

                    // 已占用不能放置
                    if (gridState === GridState.Occupied) {
                        return false;
                    }

                    // 挂饰类家具只能放在邻墙格子上
                    if (template.properties.isWallDecoration) {
                        if (gridState !== GridState.WallAdjacent1 && gridState !== GridState.WallAdjacent2) {
                            return false;
                        }
                    } else {
                        // 普通家具只能放在空地上
                        if (gridState !== GridState.Empty) {
                            return false;
                        }
                    }
                }
            }

            return true;
        }

        // 获取旋转后的尺寸
        function getRotatedSize(baseSize, rotation) {
            if (rotation === Rotation.Deg90) {
                return { x: baseSize.y, y: baseSize.x };
            }
            return { x: baseSize.x, y: baseSize.y };
        }

        // 生成家具ID
        function generateFurnitureId() {
            return 'furniture_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
        }

        // 更新格子占用状态
        function updateGridOccupancy(roomConfig, furniture, isOccupied) {
            const template = furnitureTemplates.find(t => t.id === furniture.templateId);
            if (!template) return;

            const currentSize = getRotatedSize(template.baseSize, furniture.rotation);

            // 获取原始布局
            const originalLayout = getOriginalLayout(roomConfig.id);

            for (let dx = 0; dx < currentSize.x; dx++) {
                for (let dy = 0; dy < currentSize.y; dy++) {
                    const x = furniture.position.x + dx;
                    const y = furniture.position.y + dy;

                    if (x >= 0 && x < roomConfig.size.x && y >= 0 && y < roomConfig.size.y) {
                        if (isOccupied) {
                            roomConfig.layout[y][x] = GridState.Occupied;
                        } else {
                            // 恢复原始状态
                            roomConfig.layout[y][x] = originalLayout[y][x];
                        }
                    }
                }
            }
        }

        // 获取原始布局
        function getOriginalLayout(roomId) {
            const originalLayouts = {
                roomA: [
                    [3, 3, 3, 3, 3, 0, 0, 3, 3, 3],
                    [3, 3, 3, 3, 3, 0, 0, 0, 3, 3],
                    [0, 0, 0, 3, 3, 0, 0, 0, 0, 3],
                    [0, 0, 0, 3, 3, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0, 0, 3],
                    [3, 0, 0, 0, 0, 0, 0, 0, 3, 3],
                    [3, 3, 0, 0, 0, 0, 0, 3, 3, 3]
                ],
                roomB: [
                    [0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0]
                ],
                roomC: [
                    [1, 1, 1, 1, 1],
                    [1, 1, 1, 1, 1],
                    [1, 1, 1, 1, 1],
                    [1, 1, 1, 1, 1]
                ],
                roomD: [
                    [2, 2, 2, 2, 2],
                    [2, 2, 2, 2, 2],
                    [2, 2, 2, 2, 2],
                    [2, 2, 2, 2, 2]
                ]
            };
            return originalLayouts[roomId] || [];
        }

        // 在指定位置放置家具
        function placeFurnitureAt(roomConfig, position) {
            const template = furnitureTemplates.find(t => t.id === selectedTemplateId);
            if (!template) return;

            // 检查是否可以放置
            if (!canPlaceFurniture(roomConfig, template, position)) {
                alert('无法在此位置放置家具！');
                return;
            }

            // 创建家具实例
            const furniture = {
                id: generateFurnitureId(),
                templateId: template.id,
                position: { x: position.x, y: position.y },
                rotation: Rotation.Deg0,
                currentSize: getRotatedSize(template.baseSize, Rotation.Deg0),
                placedTime: Date.now()
            };

            // 添加到房间
            roomConfig.furnitures.push(furniture);

            // 更新格子占用状态
            updateGridOccupancy(roomConfig, furniture, true);

            // 重新渲染房间
            renderRoom(roomConfig.id, roomConfig);

            // 重新计算评分
            calculateScore();
        }

        // 移除家具
        function removeFurniture(roomConfig, furnitureId) {
            const furnitureIndex = roomConfig.furnitures.findIndex(f => f.id === furnitureId);
            if (furnitureIndex === -1) return;

            const furniture = roomConfig.furnitures[furnitureIndex];

            // 清除格子占用状态
            updateGridOccupancy(roomConfig, furniture, false);

            // 从房间中移除
            roomConfig.furnitures.splice(furnitureIndex, 1);

            // 重新渲染房间
            renderRoom(roomConfig.id, roomConfig);

            // 重新计算评分
            calculateScore();
        }

        // 旋转家具
        function rotateFurniture(roomConfig, furnitureId) {
            const furniture = roomConfig.furnitures.find(f => f.id === furnitureId);
            if (!furniture) return;

            const template = furnitureTemplates.find(t => t.id === furniture.templateId);
            if (!template) return;

            // 计算新的旋转角度
            const newRotation = furniture.rotation === Rotation.Deg0 ? Rotation.Deg90 : Rotation.Deg0;
            const newSize = getRotatedSize(template.baseSize, newRotation);

            // 临时清除当前占用状态
            updateGridOccupancy(roomConfig, furniture, false);

            // 检查旋转后是否可以放置
            if (canPlaceFurniture(roomConfig, template, furniture.position, newRotation)) {
                // 更新家具信息
                furniture.rotation = newRotation;
                furniture.currentSize = newSize;

                // 更新格子占用状态
                updateGridOccupancy(roomConfig, furniture, true);

                // 重新渲染房间
                renderRoom(roomConfig.id, roomConfig);

                // 重新计算评分
                calculateScore();
            } else {
                // 恢复原来的占用状态
                updateGridOccupancy(roomConfig, furniture, true);
                alert('无法旋转家具，空间不足！');
            }
        }

        // 计算评分
        function calculateScore() {
            // 收集所有房间的家具
            const allFurnitures = [];
            Object.values(roomConfigs).forEach(roomConfig => {
                allFurnitures.push(...roomConfig.furnitures);
            });

            if (allFurnitures.length === 0) {
                updateScoreDisplay({
                    totalScore: 0,
                    themeScore: 0,
                    quantityScore: 0,
                    valueScore: 0,
                    layoutScore: 0,
                    adjacentScore: 0,
                    dominantTheme: '无'
                });
                return;
            }

            // 计算各项评分
            const themeScore = calculateThemeScore(allFurnitures);
            const quantityScore = calculateQuantityScore(allFurnitures.length);
            const valueScore = calculateValueScore(allFurnitures);
            const layoutScore = calculateLayoutScore(allFurnitures);
            const adjacentScore = calculateAdjacentScore(allFurnitures);

            // 计算总评分
            const totalScore = Math.round(
                themeScore * scoreWeights.theme +
                quantityScore * scoreWeights.quantity +
                valueScore * scoreWeights.value +
                layoutScore * scoreWeights.layout +
                adjacentScore * scoreWeights.adjacent
            );

            // 获取主导主题
            const dominantTheme = getDominantTheme(allFurnitures);

            const scoreDetails = {
                totalScore,
                themeScore: Math.round(themeScore),
                quantityScore: Math.round(quantityScore),
                valueScore: Math.round(valueScore),
                layoutScore: Math.round(layoutScore),
                adjacentScore: Math.round(adjacentScore),
                dominantTheme: getThemeName(dominantTheme)
            };

            updateScoreDisplay(scoreDetails);
        }

        // 计算主题评分
        function calculateThemeScore(furnitures) {
            if (furnitures.length === 0) return 0;

            const themeCount = {};
            furnitures.forEach(furniture => {
                const template = furnitureTemplates.find(t => t.id === furniture.templateId);
                if (template) {
                    const theme = template.properties.theme;
                    themeCount[theme] = (themeCount[theme] || 0) + 1;
                }
            });

            // 找到主导主题
            const dominantTheme = Object.keys(themeCount).reduce((a, b) =>
                themeCount[a] > themeCount[b] ? a : b
            );

            const dominantCount = themeCount[dominantTheme];
            const totalCount = furnitures.length;

            // 主题一致性评分：主导主题占比越高，分数越高
            const consistencyScore = (dominantCount / totalCount) * 100;

            // 主题多样性惩罚：主题种类过多会降低分数
            const themeTypes = Object.keys(themeCount).length;
            const diversityPenalty = Math.max(0, (themeTypes - 2) * 10);

            return Math.max(0, Math.min(100, consistencyScore - diversityPenalty));
        }

        // 计算数量评分
        function calculateQuantityScore(furnitureCount) {
            // 理想家具数量为8-12件
            const idealMin = 8;
            const idealMax = 12;

            if (furnitureCount >= idealMin && furnitureCount <= idealMax) {
                return 100;
            } else if (furnitureCount < idealMin) {
                return (furnitureCount / idealMin) * 100;
            } else {
                // 超过理想数量，分数递减
                const excess = furnitureCount - idealMax;
                return Math.max(0, 100 - excess * 5);
            }
        }

        // 计算价值评分
        function calculateValueScore(furnitures) {
            if (furnitures.length === 0) return 0;

            let totalValue = 0;
            furnitures.forEach(furniture => {
                const template = furnitureTemplates.find(t => t.id === furniture.templateId);
                if (template) {
                    totalValue += template.properties.value;
                }
            });

            const averageValue = totalValue / furnitures.length;

            // 平均价值评分：基于家具的平均价值
            return Math.min(100, averageValue * 4);
        }

        // 计算布局评分
        function calculateLayoutScore(furnitures) {
            if (furnitures.length === 0) return 0;

            let layoutScore = 0;
            let totalFurnitures = furnitures.length;

            // 按房间分组计算
            const furnituresByRoom = {};
            furnitures.forEach(furniture => {
                // 找到家具所在的房间
                const roomId = findFurnitureRoom(furniture);
                if (!furnituresByRoom[roomId]) {
                    furnituresByRoom[roomId] = [];
                }
                furnituresByRoom[roomId].push(furniture);
            });

            // 房间分布评分：家具在多个房间中分布更好
            const roomCount = Object.keys(furnituresByRoom).length;
            const distributionScore = Math.min(100, roomCount * 25);
            layoutScore += distributionScore * 0.4;

            // 空间利用率评分
            let utilizationScore = 0;
            Object.keys(furnituresByRoom).forEach(roomId => {
                const roomFurnitures = furnituresByRoom[roomId];
                const roomConfig = roomConfigs[roomId];

                // 计算房间可用空间
                const availableSpace = calculateRoomAvailableSpace(roomConfig);
                const usedSpace = roomFurnitures.reduce((sum, furniture) => {
                    return sum + (furniture.currentSize.x * furniture.currentSize.y);
                }, 0);

                if (availableSpace > 0) {
                    const utilization = Math.min(1, usedSpace / availableSpace);
                    utilizationScore += utilization * 100;
                }
            });

            if (roomCount > 0) {
                utilizationScore /= roomCount;
                layoutScore += utilizationScore * 0.6;
            }

            return Math.max(0, Math.min(100, layoutScore));
        }

        // 找到家具所在的房间
        function findFurnitureRoom(furniture) {
            for (const [roomId, roomConfig] of Object.entries(roomConfigs)) {
                if (roomConfig.furnitures.some(f => f.id === furniture.id)) {
                    return roomId;
                }
            }
            return null;
        }

        // 计算房间可用空间
        function calculateRoomAvailableSpace(roomConfig) {
            let availableCount = 0;
            for (let row of roomConfig.layout) {
                for (let cell of row) {
                    if (cell === 0) { // 只有空地可以摆放普通家具
                        availableCount++;
                    }
                }
            }
            return availableCount;
        }

        // 计算相邻评分
        function calculateAdjacentScore(furnitures) {
            if (furnitures.length <= 1) return 0;

            let totalBonus = 0;
            const processedPairs = new Set();

            furnitures.forEach((furniture, index) => {
                const adjacentFurnitures = getAdjacentFurnitures(furniture, furnitures);

                adjacentFurnitures.forEach(adjacentFurniture => {
                    const pairId = createPairId(furniture.id, adjacentFurniture.id);
                    if (processedPairs.has(pairId)) return;
                    processedPairs.add(pairId);

                    const bonus = calculatePairBonus(furniture, adjacentFurniture);
                    totalBonus += bonus;
                });
            });

            return Math.min(100, totalBonus);
        }

        // 获取相邻家具
        function getAdjacentFurnitures(targetFurniture, allFurnitures) {
            const adjacent = [];
            const targetPositions = getFurnitureOccupiedPositions(targetFurniture);

            allFurnitures.forEach(otherFurniture => {
                if (otherFurniture.id === targetFurniture.id) return;

                // 检查是否在同一房间
                const targetRoom = findFurnitureRoom(targetFurniture);
                const otherRoom = findFurnitureRoom(otherFurniture);
                if (targetRoom !== otherRoom) return; // 不同房间不算相邻

                const otherPositions = getFurnitureOccupiedPositions(otherFurniture);

                const isAdjacent = targetPositions.some(targetPos =>
                    otherPositions.some(otherPos =>
                        arePositionsAdjacent(targetPos, otherPos)
                    )
                );

                if (isAdjacent) {
                    adjacent.push(otherFurniture);
                }
            });

            return adjacent;
        }

        // 获取家具占用的所有位置
        function getFurnitureOccupiedPositions(furniture) {
            const positions = [];
            const { position, currentSize } = furniture;

            for (let x = 0; x < currentSize.x; x++) {
                for (let y = 0; y < currentSize.y; y++) {
                    positions.push({ x: position.x + x, y: position.y + y });
                }
            }

            return positions;
        }

        // 检查两个位置是否相邻
        function arePositionsAdjacent(pos1, pos2) {
            const dx = Math.abs(pos1.x - pos2.x);
            const dy = Math.abs(pos1.y - pos2.y);
            return (dx === 1 && dy === 0) || (dx === 0 && dy === 1);
        }

        // 计算两个相邻家具的奖励分数
        function calculatePairBonus(furniture1, furniture2) {
            const template1 = furnitureTemplates.find(t => t.id === furniture1.templateId);
            const template2 = furnitureTemplates.find(t => t.id === furniture2.templateId);

            if (!template1 || !template2) return 0;

            let bonus = 0;

            // 同类型家具相邻奖励
            if (template1.type === template2.type) {
                bonus += 4;
            }

            // 同主题家具相邻奖励
            if (template1.properties.theme === template2.properties.theme) {
                bonus += 3;
            }

            // 和谐主题搭配奖励
            if (areThemesHarmonious(template1.properties.theme, template2.properties.theme)) {
                bonus += 2;
            }

            // 特殊组合奖励
            const specialBonus = calculateSpecialCombinationBonus(template1, template2);
            bonus += specialBonus;

            return bonus;
        }

        // 检查两个主题是否和谐
        function areThemesHarmonious(theme1, theme2) {
            if (theme1 === theme2) return false;

            const harmoniousThemes = {
                [FurnitureTheme.Modern]: [FurnitureTheme.Minimalist, FurnitureTheme.Industrial],
                [FurnitureTheme.Classic]: [FurnitureTheme.Natural],
                [FurnitureTheme.Natural]: [FurnitureTheme.Classic, FurnitureTheme.Minimalist],
                [FurnitureTheme.Industrial]: [FurnitureTheme.Modern, FurnitureTheme.Minimalist],
                [FurnitureTheme.Minimalist]: [FurnitureTheme.Modern, FurnitureTheme.Natural, FurnitureTheme.Industrial]
            };

            const compatibleThemes = harmoniousThemes[theme1] || [];
            return compatibleThemes.includes(theme2);
        }

        // 计算特殊组合奖励
        function calculateSpecialCombinationBonus(template1, template2) {
            const specialCombinations = [
                { types: [FurnitureType.Medium, FurnitureType.Small], bonus: 3, description: "桌椅组合" },
                { types: [FurnitureType.Large, FurnitureType.Medium], bonus: 2, description: "沙发茶几组合" },
                { types: [FurnitureType.WallDecoration, FurnitureType.Small], bonus: 2, description: "挂饰装点" },
                { types: [FurnitureType.WallDecoration, FurnitureType.Medium], bonus: 2, description: "挂饰装点" },
                { types: [FurnitureType.WallDecoration, FurnitureType.Large], bonus: 1, description: "挂饰装点" }
            ];

            for (const combination of specialCombinations) {
                if ((combination.types.includes(template1.type) &&
                     combination.types.includes(template2.type)) &&
                    template1.type !== template2.type) {
                    return combination.bonus;
                }
            }

            return 0;
        }

        // 创建家具配对的唯一标识符
        function createPairId(id1, id2) {
            return id1 < id2 ? `${id1}_${id2}` : `${id2}_${id1}`;
        }

        // 获取主导主题
        function getDominantTheme(furnitures) {
            if (furnitures.length === 0) return null;

            const themeCount = {};
            furnitures.forEach(furniture => {
                const template = furnitureTemplates.find(t => t.id === furniture.templateId);
                if (template) {
                    const theme = template.properties.theme;
                    themeCount[theme] = (themeCount[theme] || 0) + 1;
                }
            });

            return Object.keys(themeCount).reduce((a, b) =>
                themeCount[a] > themeCount[b] ? a : b
            );
        }

        // 获取主题名称
        function getThemeName(theme) {
            const themeNames = {
                [FurnitureTheme.Modern]: '现代风格',
                [FurnitureTheme.Classic]: '古典风格',
                [FurnitureTheme.Natural]: '自然风格',
                [FurnitureTheme.Industrial]: '工业风格',
                [FurnitureTheme.Minimalist]: '简约风格'
            };
            return themeNames[theme] || '未知';
        }

        // 更新评分显示
        function updateScoreDisplay(scoreDetails) {
            document.getElementById('totalScore').textContent = scoreDetails.totalScore;
            document.getElementById('themeScore').textContent = scoreDetails.themeScore;
            document.getElementById('quantityScore').textContent = scoreDetails.quantityScore;
            document.getElementById('valueScore').textContent = scoreDetails.valueScore;
            document.getElementById('layoutScore').textContent = scoreDetails.layoutScore;
            document.getElementById('adjacentScore').textContent = scoreDetails.adjacentScore;
            document.getElementById('dominantTheme').textContent = scoreDetails.dominantTheme;
        }

        // 清空所有家具
        function clearAllFurniture() {
            if (!confirm('确定要清空所有家具吗？')) return;

            // 重置所有房间
            Object.keys(roomConfigs).forEach(roomId => {
                roomConfigs[roomId].furnitures = [];
                roomConfigs[roomId].layout = JSON.parse(JSON.stringify(getOriginalLayout(roomId)));
            });

            // 重新渲染所有房间
            Object.keys(roomConfigs).forEach(roomId => {
                renderRoom(roomId, roomConfigs[roomId]);
            });

            // 重新计算评分
            calculateScore();
        }

        // 重置房间
        function resetRooms() {
            if (!confirm('确定要重置所有房间吗？')) return;

            clearAllFurniture();
        }

        // 开始拖拽
        function startDrag(e, furniture, roomConfig) {
            e.preventDefault();

            draggedFurniture = furniture;
            draggedFurnitureRoom = roomConfig;
            isDragging = true;

            // 计算鼠标相对于家具的偏移量
            const furnitureElement = e.target;
            const rect = furnitureElement.getBoundingClientRect();
            dragOffset.x = e.clientX - rect.left;
            dragOffset.y = e.clientY - rect.top;

            // 添加拖拽样式
            furnitureElement.classList.add('dragging');

            // 临时移除家具的占用状态
            updateGridOccupancy(roomConfig, furniture, false);

            // 添加全局鼠标事件
            document.addEventListener('mousemove', handleDragMove);
            document.addEventListener('mouseup', handleDragEnd);

            // 重新渲染房间以更新格子状态
            renderRoom(roomConfig.id, roomConfig);
        }

        // 处理拖拽移动
        function handleDragMove(e) {
            if (!isDragging || !draggedFurniture) return;

            // 更新家具位置跟随鼠标
            const furnitureElement = document.querySelector(`[data-furniture-id="${draggedFurniture.id}"]`);
            if (furnitureElement) {
                furnitureElement.style.left = (e.clientX - dragOffset.x) + 'px';
                furnitureElement.style.top = (e.clientY - dragOffset.y) + 'px';
                furnitureElement.style.position = 'fixed';
                furnitureElement.style.zIndex = '1000';
                furnitureElement.style.pointerEvents = 'none'; // 确保不阻挡鼠标事件
            }

            // 清除所有格子的拖拽状态
            document.querySelectorAll('.grid-cell').forEach(cell => {
                cell.classList.remove('drag-over', 'drag-invalid');
            });

            // 检查鼠标下的格子（临时隐藏家具元素）
            let elementUnderMouse = null;
            if (furnitureElement) {
                const originalDisplay = furnitureElement.style.display;
                furnitureElement.style.display = 'none';
                elementUnderMouse = document.elementFromPoint(e.clientX, e.clientY);
                furnitureElement.style.display = originalDisplay || '';
            }

            if (elementUnderMouse && elementUnderMouse.classList.contains('grid-cell')) {
                const x = parseInt(elementUnderMouse.dataset.x);
                const y = parseInt(elementUnderMouse.dataset.y);
                const roomId = elementUnderMouse.dataset.roomId;
                const targetRoom = roomConfigs[roomId];

                if (targetRoom && !isNaN(x) && !isNaN(y)) {
                    const template = furnitureTemplates.find(t => t.id === draggedFurniture.templateId);
                    const newPosition = { x, y };

                    // 检查是否可以放置
                    if (canPlaceFurniture(targetRoom, template, newPosition, draggedFurniture.rotation)) {
                        // 高亮可放置的区域
                        highlightPlacementArea(targetRoom, newPosition, draggedFurniture, 'drag-over');
                    } else {
                        // 高亮不可放置的区域
                        highlightPlacementArea(targetRoom, newPosition, draggedFurniture, 'drag-invalid');
                    }
                }
            }
        }

        // 处理拖拽结束
        function handleDragEnd(e) {
            if (!isDragging || !draggedFurniture) return;

            // 移除全局事件监听
            document.removeEventListener('mousemove', handleDragMove);
            document.removeEventListener('mouseup', handleDragEnd);

            // 临时隐藏拖拽的家具元素，以便正确检测下方的格子
            const furnitureElement = document.querySelector(`[data-furniture-id="${draggedFurniture.id}"]`);
            let originalDisplay = null;
            if (furnitureElement) {
                originalDisplay = furnitureElement.style.display;
                furnitureElement.style.display = 'none';
            }

            // 获取鼠标下的元素
            const elementUnderMouse = document.elementFromPoint(e.clientX, e.clientY);
            let dropSuccess = false;

            // 恢复家具元素显示
            if (furnitureElement) {
                furnitureElement.style.display = originalDisplay || '';
            }

            if (elementUnderMouse && elementUnderMouse.classList.contains('grid-cell')) {
                const x = parseInt(elementUnderMouse.dataset.x);
                const y = parseInt(elementUnderMouse.dataset.y);
                const roomId = elementUnderMouse.dataset.roomId;
                const targetRoom = roomConfigs[roomId];

                if (targetRoom && !isNaN(x) && !isNaN(y)) {
                    const template = furnitureTemplates.find(t => t.id === draggedFurniture.templateId);
                    const newPosition = { x, y };

                    console.log('尝试放置家具到:', roomId, newPosition, '当前房间:', draggedFurnitureRoom.id);

                    // 检查是否可以放置
                    if (canPlaceFurniture(targetRoom, template, newPosition, draggedFurniture.rotation)) {
                        console.log('可以放置，开始移动家具');

                        // 如果是不同房间，需要从原房间移除
                        if (targetRoom.id !== draggedFurnitureRoom.id) {
                            const furnitureIndex = draggedFurnitureRoom.furnitures.findIndex(f => f.id === draggedFurniture.id);
                            if (furnitureIndex !== -1) {
                                draggedFurnitureRoom.furnitures.splice(furnitureIndex, 1);
                                targetRoom.furnitures.push(draggedFurniture);
                                console.log('家具已从', draggedFurnitureRoom.id, '移动到', targetRoom.id);
                            }
                        }

                        // 更新家具位置
                        draggedFurniture.position = newPosition;

                        // 更新格子占用状态
                        updateGridOccupancy(targetRoom, draggedFurniture, true);

                        dropSuccess = true;
                        console.log('家具放置成功');
                    } else {
                        console.log('无法放置家具到该位置');
                    }
                } else {
                    console.log('无效的目标房间或位置');
                }
            } else {
                console.log('鼠标下没有找到格子元素');
            }

            // 如果拖拽失败，恢复原来的占用状态
            if (!dropSuccess) {
                updateGridOccupancy(draggedFurnitureRoom, draggedFurniture, true);
            }

            // 清除所有格子的拖拽状态
            document.querySelectorAll('.grid-cell').forEach(cell => {
                cell.classList.remove('drag-over', 'drag-invalid');
            });

            // 重新渲染所有房间
            Object.keys(roomConfigs).forEach(roomId => {
                renderRoom(roomId, roomConfigs[roomId]);
            });

            // 重新计算评分
            calculateScore();

            // 重置拖拽状态
            draggedFurniture = null;
            draggedFurnitureRoom = null;
            isDragging = false;
        }

        // 高亮放置区域
        function highlightPlacementArea(roomConfig, position, furniture, className) {
            const template = furnitureTemplates.find(t => t.id === furniture.templateId);
            if (!template) return;

            const currentSize = getRotatedSize(template.baseSize, furniture.rotation);

            for (let dx = 0; dx < currentSize.x; dx++) {
                for (let dy = 0; dy < currentSize.y; dy++) {
                    const checkX = position.x + dx;
                    const checkY = position.y + dy;

                    if (checkX >= 0 && checkX < roomConfig.size.x &&
                        checkY >= 0 && checkY < roomConfig.size.y) {

                        const cell = document.querySelector(
                            `[data-room-id="${roomConfig.id}"][data-x="${checkX}"][data-y="${checkY}"]`
                        );

                        if (cell) {
                            cell.classList.add(className);
                        }
                    }
                }
            }
        }
    </script>
</body>
</html>
