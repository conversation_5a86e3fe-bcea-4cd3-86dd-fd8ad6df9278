/**
 * DefaultRoomTemplates.fixRoomTemplates 方法验证测试
 * 验证房间模板修正功能的正确性
 */

import { Component, Vec2, _decorator } from "cc";
import { DefaultRoomTemplates } from "../logic/DefaultRoomTemplates";
import { RoomTemplate } from "../logic/DecorationDefine";

const { ccclass, property } = _decorator;

@ccclass('FixRoomTemplatesTest')
export class FixRoomTemplatesTest extends Component {
    
    start() {
        console.log("=== DefaultRoomTemplates.fixRoomTemplates 方法验证测试开始 ===");
        
        try {
            this.testFixRoomTemplatesLogic();
            this.testBoundaryProcessing();
            this.testObstacleAdjacentProcessing();
            this.testOriginalTemplatesIntegrity();
            this.testEdgeCases();
            this.testAllDefaultTemplates();
            
            console.log("=== DefaultRoomTemplates.fixRoomTemplates 方法验证测试完成 ===");
        } catch (error) {
            console.error("测试过程中出现错误:", error);
        }
    }

    /**
     * 测试 fixRoomTemplates 的基本逻辑
     */
    private testFixRoomTemplatesLogic() {
        console.log("--- 测试 fixRoomTemplates 基本逻辑 ---");
        
        // 创建测试用的房间模板
        const testTemplate: RoomTemplate = {
            id: "test_room",
            name: "测试房间",
            size: new Vec2(5, 5),
            layout: [
                [0, 0, 0, 0, 0],
                [0, 0, 2, 0, 0],
                [0, 2, 2, 2, 0],
                [0, 0, 2, 0, 0],
                [0, 0, 0, 0, 0]
            ]
        };

        console.log("原始布局:");
        this.printLayout(testTemplate.layout, "原始");

        // 应用修正
        const fixedTemplate = DefaultRoomTemplates.fixRoomTemplates(testTemplate);
        
        console.log("修正后布局:");
        this.printLayout(fixedTemplate.layout, "修正后");

        // 验证边界是否正确设置为1
        this.verifyBoundaries(fixedTemplate.layout);
        
        // 验证障碍物邻接格子是否正确设置为1
        this.verifyObstacleAdjacent(fixedTemplate.layout);
    }

    /**
     * 测试边界处理
     */
    private testBoundaryProcessing() {
        console.log("--- 测试边界处理 ---");
        
        const testTemplate: RoomTemplate = {
            id: "boundary_test",
            name: "边界测试",
            size: new Vec2(4, 4),
            layout: [
                [0, 0, 0, 0],
                [0, 0, 0, 0],
                [0, 0, 0, 0],
                [0, 0, 0, 0]
            ]
        };

        const fixedTemplate = DefaultRoomTemplates.fixRoomTemplates(testTemplate);
        
        console.log("边界处理测试结果:");
        this.printLayout(fixedTemplate.layout, "边界处理");

        // 验证所有边界格子都是1
        const layout = fixedTemplate.layout;
        const height = layout.length;
        const width = layout[0].length;

        let boundaryCorrect = true;
        
        // 检查上下边界
        for (let x = 0; x < width; x++) {
            if (layout[0][x] !== 1 || layout[height - 1][x] !== 1) {
                boundaryCorrect = false;
                console.error(`边界错误: 上下边界位置 (${x}) 不是1`);
            }
        }
        
        // 检查左右边界
        for (let y = 0; y < height; y++) {
            if (layout[y][0] !== 1 || layout[y][width - 1] !== 1) {
                boundaryCorrect = false;
                console.error(`边界错误: 左右边界位置 (${y}) 不是1`);
            }
        }

        console.log(`边界处理验证: ${boundaryCorrect ? "通过" : "失败"}`);
    }

    /**
     * 测试障碍物邻接处理
     */
    private testObstacleAdjacentProcessing() {
        console.log("--- 测试障碍物邻接处理 ---");
        
        const testTemplate: RoomTemplate = {
            id: "obstacle_test",
            name: "障碍物测试",
            size: new Vec2(6, 6),
            layout: [
                [1, 1, 1, 1, 1, 1],
                [1, 0, 2, 0, 0, 1],
                [1, 0, 2, 2, 0, 1],
                [1, 0, 0, 2, 0, 1],
                [1, 0, 0, 0, 0, 1],
                [1, 1, 1, 1, 1, 1]
            ]
        };

        console.log("障碍物邻接测试 - 原始布局:");
        this.printLayout(testTemplate.layout, "原始");

        const fixedTemplate = DefaultRoomTemplates.fixRoomTemplates(testTemplate);
        
        console.log("障碍物邻接测试 - 修正后布局:");
        this.printLayout(fixedTemplate.layout, "修正后");

        // 验证障碍物邻接的空地格子是否变成1
        this.verifyObstacleAdjacent(fixedTemplate.layout);
    }

    /**
     * 测试原始模板的完整性
     */
    private testOriginalTemplatesIntegrity() {
        console.log("--- 测试原始模板完整性 ---");
        
        // 测试所有默认房间模板
        const templates = [
            { name: "房间A", template: DefaultRoomTemplates.getRoomATemplate() },
            { name: "房间B", template: DefaultRoomTemplates.getRoomBTemplate() },
        ];

        templates.forEach(({ name, template }) => {
            console.log(`\n${name} 原始布局:`);
            this.printLayout(template.layout, name);
            
            // 应用修正
            const fixedTemplate = DefaultRoomTemplates.fixRoomTemplates(JSON.parse(JSON.stringify(template)));
            
            console.log(`${name} 修正后布局:`);
            this.printLayout(fixedTemplate.layout, `${name}修正后`);
            
            // 验证修正结果
            const isValid = this.validateFixedTemplate(fixedTemplate);
            console.log(`${name} 修正验证: ${isValid ? "通过" : "失败"}`);
        });
    }

    /**
     * 测试边界情况
     */
    private testEdgeCases() {
        console.log("--- 测试边界情况 ---");
        
        // 测试最小房间 (2x2)
        const minTemplate: RoomTemplate = {
            id: "min_room",
            name: "最小房间",
            size: new Vec2(2, 2),
            layout: [
                [0, 0],
                [0, 0]
            ]
        };

        const fixedMinTemplate = DefaultRoomTemplates.fixRoomTemplates(minTemplate);
        console.log("最小房间修正结果:");
        this.printLayout(fixedMinTemplate.layout, "最小房间");

        // 测试全障碍房间
        const obstacleTemplate: RoomTemplate = {
            id: "obstacle_room",
            name: "障碍房间",
            size: new Vec2(3, 3),
            layout: [
                [2, 2, 2],
                [2, 0, 2],
                [2, 2, 2]
            ]
        };

        const fixedObstacleTemplate = DefaultRoomTemplates.fixRoomTemplates(obstacleTemplate);
        console.log("障碍房间修正结果:");
        this.printLayout(fixedObstacleTemplate.layout, "障碍房间");

        // 测试单行房间
        const singleRowTemplate: RoomTemplate = {
            id: "single_row",
            name: "单行房间",
            size: new Vec2(5, 1),
            layout: [
                [0, 0, 0, 0, 0]
            ]
        };

        const fixedSingleRowTemplate = DefaultRoomTemplates.fixRoomTemplates(singleRowTemplate);
        console.log("单行房间修正结果:");
        this.printLayout(fixedSingleRowTemplate.layout, "单行房间");
    }

    /**
     * 测试所有默认模板
     */
    private testAllDefaultTemplates() {
        console.log("--- 测试所有默认模板 ---");
        
        const allTemplates = DefaultRoomTemplates.getAllTemplates();
        console.log(`获取到 ${allTemplates.length} 个默认模板`);

        allTemplates.forEach((template, index) => {
            console.log(`\n模板 ${index + 1}: ${template.name} (${template.size.x}×${template.size.y})`);
            
            // 验证模板的有效性
            const isValid = this.validateFixedTemplate(template);
            console.log(`模板有效性: ${isValid ? "有效" : "无效"}`);
            
            // 统计格子类型
            const stats = this.calculateLayoutStats(template.layout);
            console.log(`格子统计: 空地=${stats.empty}, 邻墙=${stats.wallAdjacent}, 障碍=${stats.obstacle}`);
        });
    }

    /**
     * 验证边界设置
     */
    private verifyBoundaries(layout: number[][]): boolean {
        const height = layout.length;
        const width = layout[0].length;
        let isValid = true;

        // 检查上下边界
        for (let x = 0; x < width; x++) {
            if (layout[0][x] !== 2 && layout[0][x] !== 1) {
                console.error(`上边界错误: (0,${x}) = ${layout[0][x]}`);
                isValid = false;
            }
            if (layout[height - 1][x] !== 2 && layout[height - 1][x] !== 1) {
                console.error(`下边界错误: (${height - 1},${x}) = ${layout[height - 1][x]}`);
                isValid = false;
            }
        }

        // 检查左右边界
        for (let y = 0; y < height; y++) {
            if (layout[y][0] !== 2 && layout[y][0] !== 1) {
                console.error(`左边界错误: (${y},0) = ${layout[y][0]}`);
                isValid = false;
            }
            if (layout[y][width - 1] !== 2 && layout[y][width - 1] !== 1) {
                console.error(`右边界错误: (${y},${width - 1}) = ${layout[y][width - 1]}`);
                isValid = false;
            }
        }

        return isValid;
    }

    /**
     * 验证障碍物邻接处理
     */
    private verifyObstacleAdjacent(layout: number[][]): boolean {
        const height = layout.length;
        const width = layout[0].length;
        let isValid = true;

        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                if (layout[y][x] === 0) {
                    // 检查四个方向是否有障碍物
                    const hasObstacleAdjacent = 
                        layout[y][x + 1] === 2 ||
                        layout[y][x - 1] === 2 ||
                        layout[y + 1][x] === 2 ||
                        layout[y - 1][x] === 2;

                    if (hasObstacleAdjacent) {
                        console.error(`障碍物邻接错误: (${y},${x}) 应该是1但是是0`);
                        isValid = false;
                    }
                }
            }
        }

        return isValid;
    }

    /**
     * 验证修正后的模板
     */
    private validateFixedTemplate(template: RoomTemplate): boolean {
        const boundaryValid = this.verifyBoundaries(template.layout);
        const obstacleValid = this.verifyObstacleAdjacent(template.layout);
        return boundaryValid && obstacleValid;
    }

    /**
     * 计算布局统计信息
     */
    private calculateLayoutStats(layout: number[][]): {empty: number, wallAdjacent: number, obstacle: number} {
        let empty = 0, wallAdjacent = 0, obstacle = 0;

        for (let y = 0; y < layout.length; y++) {
            for (let x = 0; x < layout[y].length; x++) {
                switch (layout[y][x]) {
                    case 0: empty++; break;
                    case 1: wallAdjacent++; break;
                    case 2: obstacle++; break;
                }
            }
        }

        return { empty, wallAdjacent, obstacle };
    }

    /**
     * 打印布局（用于调试）
     */
    private printLayout(layout: number[][], title: string) {
        console.log(`${title} 布局:`);
        for (let y = 0; y < layout.length; y++) {
            let row = "";
            for (let x = 0; x < layout[y].length; x++) {
                const cell = layout[y][x];
                row += cell === 0 ? "·" : (cell === 1 ? "○" : "█");
            }
            console.log(`  ${row}`);
        }
        console.log("  (· = 空地, ○ = 邻墙, █ = 障碍)");
    }

    /**
     * 手动触发完整测试
     */
    public runFullTest() {
        this.start();
    }
}
