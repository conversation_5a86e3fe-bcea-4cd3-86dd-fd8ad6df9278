[{"__type__": "cc.SceneAsset", "_name": "testScene", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_name": "testScene", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [], "_prefab": {"__id__": 48}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "autoReleaseAssets": false, "_globals": {"__id__": 49}, "_id": "ec6af288-8b15-4d60-94a6-c6606f243cd9"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 5}], "_active": true, "_components": [{"__id__": 45}, {"__id__": 46}, {"__id__": 47}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 540, "y": 960, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "beI88Z2HpFELqR4T5EMHpg"}, {"__type__": "cc.Node", "_name": "Camera", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 1000}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ebFwiq8gBFaYpqYbdoDODe"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": null, "_projection": 0, "_priority": 0, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 960, "_near": 0, "_far": 2000, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 7, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 1108344832, "_targetTexture": null, "_postProcess": null, "_usePostProcess": false, "_cameraType": -1, "_trackingType": 0, "_id": "63WIch3o5BEYRlXzTT0oWc"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 2}, "_prefab": {"__id__": 6}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 5}, "asset": {"__uuid__": "2defaeed-cae4-4927-833f-15bb30985567", "__expectedType__": "cc.Prefab"}, "fileId": "8e860U3x8hPraxHkqaFtNvf", "instance": {"__id__": 7}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "eeE5XAHr1MJqxyjy+5MONG", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 8}, {"__id__": 10}, {"__id__": 11}, {"__id__": 12}, {"__id__": 13}, {"__id__": 15}, {"__id__": 17}, {"__id__": 19}, {"__id__": 21}, {"__id__": 23}, {"__id__": 25}, {"__id__": 27}, {"__id__": 29}, {"__id__": 31}, {"__id__": 33}, {"__id__": 35}, {"__id__": 37}, {"__id__": 39}, {"__id__": 41}, {"__id__": 43}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 9}, "propertyPath": ["_name"], "value": "shinei"}, {"__type__": "cc.TargetInfo", "localID": ["8e860U3x8hPraxHkqaFtNvf"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 9}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 9}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 9}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 14}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["e2c32ZIaMZIaqVvJJpPJPMD"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 16}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["d5c865eJytEwI5DapxeO96+"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 18}, "propertyPath": ["_active"], "value": true}, {"__type__": "cc.TargetInfo", "localID": ["569e8z5iVJFArtdO2uksCg4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 20}, "propertyPath": ["_decorationType"], "value": 3}, {"__type__": "cc.TargetInfo", "localID": ["9czdFX4PRKY5fyE26vxWPA", "8dMAdN+dpGJLsqiQ3CrzSQ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 22}, "propertyPath": ["_decorationType"], "value": 3}, {"__type__": "cc.TargetInfo", "localID": ["63IR3cjr9OXr1MO49bBqij", "8dMAdN+dpGJLsqiQ3CrzSQ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 24}, "propertyPath": ["_decorationType"], "value": 3}, {"__type__": "cc.TargetInfo", "localID": ["ff82Qul8NFV4LHRFQLDhDr", "8dMAdN+dpGJLsqiQ3CrzSQ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 26}, "propertyPath": ["_decorationType"], "value": 3}, {"__type__": "cc.TargetInfo", "localID": ["46RGC4+N5IDLhFpV32iYvN", "8dMAdN+dpGJLsqiQ3CrzSQ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 28}, "propertyPath": ["_decorationType"], "value": 0}, {"__type__": "cc.TargetInfo", "localID": ["296QoKga1JQY5bS6fSdIz7", "8dMAdN+dpGJLsqiQ3CrzSQ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 30}, "propertyPath": ["_decorationType"], "value": 3}, {"__type__": "cc.TargetInfo", "localID": ["824HffhllKpLE4DCYaD2X9", "8dMAdN+dpGJLsqiQ3CrzSQ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 32}, "propertyPath": ["_decorationType"], "value": 0}, {"__type__": "cc.TargetInfo", "localID": ["deqElX3+dP1oJlRMpMRVLd", "8dMAdN+dpGJLsqiQ3CrzSQ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 34}, "propertyPath": ["_decorationType"], "value": 3}, {"__type__": "cc.TargetInfo", "localID": ["57jfKjDOVCwKEs0g70NkYj", "8dMAdN+dpGJLsqiQ3CrzSQ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 36}, "propertyPath": ["_decorationType"], "value": 3}, {"__type__": "cc.TargetInfo", "localID": ["69ipmbFb1EqJ/PvWyEGV08", "8dMAdN+dpGJLsqiQ3CrzSQ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 38}, "propertyPath": ["_decorationType"], "value": 3}, {"__type__": "cc.TargetInfo", "localID": ["29L/3jdGlGs5KS1OUl3QsX", "8dMAdN+dpGJLsqiQ3CrzSQ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 40}, "propertyPath": ["_decorationType"], "value": 3}, {"__type__": "cc.TargetInfo", "localID": ["6epaWxAcxB9ZVoRWdU/Oms", "8dMAdN+dpGJLsqiQ3CrzSQ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 42}, "propertyPath": ["_active"], "value": true}, {"__type__": "cc.TargetInfo", "localID": ["a41oyd9TFB/4855fCsR4eB"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 44}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 140.738, "y": -182.748, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["1ftsnrUMtOsaCmP+FLTMD4"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d6rUX5yfhMlKoWX2bSbawx"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_cameraComponent": {"__id__": 4}, "_alignCanvasWithScreen": true, "_id": "12O/ljcVlEqLmVm3U2gEOQ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 5.684341886080802e-14, "_bottom": 5.684341886080802e-14, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "c5V1EV8IpMtrIvY1OE9t2u"}, {"__type__": "cc.PrefabInfo", "root": null, "asset": null, "fileId": "ec6af288-8b15-4d60-94a6-c6606f243cd9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 5}]}, {"__type__": "cc.SceneGlobals", "ambient": {"__id__": 50}, "shadows": {"__id__": 51}, "_skybox": {"__id__": 52}, "fog": {"__id__": 53}, "octree": {"__id__": 54}, "skin": {"__id__": 55}, "lightProbeInfo": {"__id__": 56}, "postSettings": {"__id__": 57}, "bakedWithStationaryMainLight": false, "bakedWithHighpLightmap": false}, {"__type__": "cc.AmbientInfo", "_skyColorHDR": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0.520833125}, "_skyColor": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0.520833125}, "_skyIllumHDR": 20000, "_skyIllum": 20000, "_groundAlbedoHDR": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_groundAlbedo": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_skyColorLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 1}, "_skyIllumLDR": 20000, "_groundAlbedoLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}}, {"__type__": "cc.ShadowsInfo", "_enabled": false, "_type": 0, "_normal": {"__type__": "cc.Vec3", "x": 0, "y": 1, "z": 0}, "_distance": 0, "_planeBias": 1, "_shadowColor": {"__type__": "cc.Color", "r": 76, "g": 76, "b": 76, "a": 255}, "_maxReceived": 4, "_size": {"__type__": "cc.Vec2", "x": 512, "y": 512}}, {"__type__": "cc.SkyboxInfo", "_envLightingType": 0, "_envmapHDR": null, "_envmap": null, "_envmapLDR": null, "_diffuseMapHDR": null, "_diffuseMapLDR": null, "_enabled": false, "_useHDR": true, "_editableMaterial": null, "_reflectionHDR": null, "_reflectionLDR": null, "_rotationAngle": 0}, {"__type__": "cc.FogInfo", "_type": 0, "_fogColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_enabled": false, "_fogDensity": 0.3, "_fogStart": 0.5, "_fogEnd": 300, "_fogAtten": 5, "_fogTop": 1.5, "_fogRange": 1.2, "_accurate": false}, {"__type__": "cc.OctreeInfo", "_enabled": false, "_minPos": {"__type__": "cc.Vec3", "x": -1024, "y": -1024, "z": -1024}, "_maxPos": {"__type__": "cc.Vec3", "x": 1024, "y": 1024, "z": 1024}, "_depth": 8}, {"__type__": "cc.SkinInfo", "_enabled": false, "_blurRadius": 0.01, "_sssIntensity": 3}, {"__type__": "cc.LightProbeInfo", "_giScale": 1, "_giSamples": 1024, "_bounces": 2, "_reduceRinging": 0, "_showProbe": true, "_showWireframe": true, "_showConvex": false, "_data": null, "_lightProbeSphereVolume": 1}, {"__type__": "cc.PostSettingsInfo", "_toneMappingType": 0}]