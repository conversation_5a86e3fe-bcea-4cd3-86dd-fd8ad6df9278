import { FurnitureTemplate, FurnitureType, FurnitureTheme } from "../logic/DecorationDefine";
import { Vec2 } from "cc";

/**
 * 家具模板数据
 * 包含所有可用的家具模板定义
 */
export class FurnitureTemplates {
    /**
     * 获取所有家具模板
     */
    static getAllTemplates(): FurnitureTemplate[] {
        return [
            {
                id: 1,
                name: "现代椅子",
                type: FurnitureType.Small,
                baseSize: new Vec2(1, 1),
                spriteFrame: "texture/furniture/modern_chair",
                description: "简洁现代的椅子，舒适实用",
                properties: {
                    theme: FurnitureTheme.Modern,
                    level: 1,
                    value: 8,
                    beauty: 6,
                    isWallDecoration: false
                }
            },
            {
                id: 2,
                name: "古典床",
                type: FurnitureType.Medium,
                baseSize: new Vec2(2, 1),
                spriteFrame: "texture/furniture/classic_bed",
                description: "优雅的古典风格床铺",
                properties: {
                    theme: FurnitureTheme.Classic,
                    level: 2,
                    value: 15,
                    beauty: 8,
                    isWallDecoration: false
                }
            },
            {
                id: 3,
                name: "工业风桌子",
                type: FurnitureType.Large,
                baseSize: new Vec2(2, 2),
                spriteFrame: "texture/furniture/industrial_table",
                description: "粗犷的工业风格桌子",
                properties: {
                    theme: FurnitureTheme.Industrial,
                    level: 2,
                    value: 12,
                    beauty: 5,
                    isWallDecoration: false
                }
            },
            {
                id: 4,
                name: "简约台灯",
                type: FurnitureType.Small,
                baseSize: new Vec2(1, 1),
                spriteFrame: "texture/furniture/minimalist_lamp",
                description: "简约风格的台灯，线条简洁",
                properties: {
                    theme: FurnitureTheme.Minimalist,
                    level: 1,
                    value: 6,
                    beauty: 7,
                    isWallDecoration: false
                }
            },
            {
                id: 5,
                name: "自然风沙发",
                type: FurnitureType.Medium,
                baseSize: new Vec2(2, 1),
                spriteFrame: "texture/furniture/natural_sofa",
                description: "天然材质的舒适沙发",
                properties: {
                    theme: FurnitureTheme.Natural,
                    level: 3,
                    value: 20,
                    beauty: 9,
                    isWallDecoration: false
                }
            },
            {
                id: 6,
                name: "古典书柜",
                type: FurnitureType.Large,
                baseSize: new Vec2(2, 2),
                spriteFrame: "texture/furniture/classic_bookshelf",
                description: "典雅的古典书柜，知识的象征",
                properties: {
                    theme: FurnitureTheme.Classic,
                    level: 3,
                    value: 25,
                    beauty: 10,
                    isWallDecoration: false
                }
            },
            {
                id: 7,
                name: "衣柜",
                type: FurnitureType.Large,
                baseSize: new Vec2(2, 2),
                spriteFrame: "texture/furniture/wardrobe",
                description: "整洁的衣柜，提供全属性小幅加成",
                properties: {
                    theme: FurnitureTheme.Modern,
                    level: 2,
                    value: 18,
                    beauty: 8,
                    isWallDecoration: false
                }
            },
            {
                id: 8,
                name: "花盆",
                type: FurnitureType.Small,
                baseSize: new Vec2(1, 1),
                spriteFrame: "texture/furniture/plant",
                description: "清新的植物，提供生命值加成",
                properties: {
                    theme: FurnitureTheme.Natural,
                    level: 1,
                    value: 5,
                    beauty: 6,
                    isWallDecoration: false
                }
            },
            {
                id: 9,
                name: "工作台",
                type: FurnitureType.Medium,
                baseSize: new Vec2(2, 1),
                spriteFrame: "texture/furniture/workbench",
                description: "实用的工作台，提供攻击力和收益加成",
                properties: {
                    theme: FurnitureTheme.Industrial,
                    level: 2,
                    value: 14,
                    beauty: 4,
                    isWallDecoration: false
                }
            },
            {
                id: 10,
                name: "钢琴",
                type: FurnitureType.Large,
                baseSize: new Vec2(2, 2),
                spriteFrame: "texture/furniture/piano",
                description: "优雅的钢琴，提供大幅收益加成",
                properties: {
                    theme: FurnitureTheme.Classic,
                    level: 3,
                    value: 30,
                    beauty: 12,
                    isWallDecoration: false
                }
            },
            {
                id: 11,
                name: "现代壁画",
                type: FurnitureType.WallDecoration,
                baseSize: new Vec2(1, 1),
                spriteFrame: "texture/furniture/modern_painting",
                description: "现代风格的装饰画，只能摆放在墙上",
                properties: {
                    theme: FurnitureTheme.Modern,
                    level: 2,
                    value: 12,
                    beauty: 10,
                    isWallDecoration: true
                }
            },
            {
                id: 12,
                name: "古典挂钟",
                type: FurnitureType.WallDecoration,
                baseSize: new Vec2(1, 1),
                spriteFrame: "texture/furniture/classic_clock",
                description: "古典风格的挂钟，只能摆放在墙上",
                properties: {
                    theme: FurnitureTheme.Classic,
                    level: 2,
                    value: 10,
                    beauty: 8,
                    isWallDecoration: true
                }
            },
            {
                id: 13,
                name: "工业风金属装饰",
                type: FurnitureType.WallDecoration,
                baseSize: new Vec2(2, 1),
                spriteFrame: "texture/furniture/industrial_metal_decor",
                description: "工业风格的金属装饰品，2×1尺寸，必须贴墙摆放",
                properties: {
                    theme: FurnitureTheme.Industrial,
                    level: 3,
                    value: 18,
                    beauty: 12,
                    isWallDecoration: true
                }
            },
            {
                id: 14,
                name: "简约艺术挂件",
                type: FurnitureType.WallDecoration,
                baseSize: new Vec2(2, 1),
                spriteFrame: "texture/furniture/minimalist_art_piece",
                description: "简约风格的艺术挂件，2×1尺寸，必须贴墙摆放",
                properties: {
                    theme: FurnitureTheme.Minimalist,
                    level: 3,
                    value: 20,
                    beauty: 14,
                    isWallDecoration: true
                }
            },
            {
                id: 15,
                name: "自然风植物壁挂",
                type: FurnitureType.WallDecoration,
                baseSize: new Vec2(2, 2),
                spriteFrame: "texture/furniture/natural_plant_wall",
                description: "自然风格的植物壁挂装饰，2×2尺寸，必须贴墙摆放",
                properties: {
                    theme: FurnitureTheme.Natural,
                    level: 4,
                    value: 35,
                    beauty: 20,
                    isWallDecoration: true
                }
            },
            {
                id: 16,
                name: "现代抽象画组",
                type: FurnitureType.WallDecoration,
                baseSize: new Vec2(2, 2),
                spriteFrame: "texture/furniture/modern_abstract_group",
                description: "现代风格的抽象画组合，2×2尺寸，必须贴墙摆放",
                properties: {
                    theme: FurnitureTheme.Modern,
                    level: 4,
                    value: 40,
                    beauty: 22,
                    isWallDecoration: true
                }
            },
            {
                id: 17,
                name: "古典木门",
                type: FurnitureType.WallAdjacent,
                baseSize: new Vec2(1, 2),
                spriteFrame: "texture/furniture/classic_wooden_door",
                description: "古典风格的木门，1×2尺寸，只能放在邻墙格子",
                properties: {
                    theme: FurnitureTheme.Classic,
                    level: 3,
                    value: 25,
                    beauty: 15,
                    isWallAdjacent: true
                }
            },
            {
                id: 18,
                name: "现代玻璃门",
                type: FurnitureType.WallAdjacent,
                baseSize: new Vec2(1, 2),
                spriteFrame: "texture/furniture/modern_glass_door",
                description: "现代风格的玻璃门，1×2尺寸，只能放在邻墙格子",
                properties: {
                    theme: FurnitureTheme.Modern,
                    level: 3,
                    value: 30,
                    beauty: 18,
                    isWallAdjacent: true
                }
            },
            {
                id: 19,
                name: "工业风铁门",
                type: FurnitureType.WallAdjacent,
                baseSize: new Vec2(1, 2),
                spriteFrame: "texture/furniture/industrial_iron_door",
                description: "工业风格的铁门，1×2尺寸，只能放在邻墙格子",
                properties: {
                    theme: FurnitureTheme.Industrial,
                    level: 2,
                    value: 20,
                    beauty: 12,
                    isWallAdjacent: true
                }
            },
            {
                id: 20,
                name: "自然风飘台",
                type: FurnitureType.WallAdjacent,
                baseSize: new Vec2(2, 1),
                spriteFrame: "texture/furniture/natural_bay_window",
                description: "自然风格的飘台，2×1尺寸，只能放在邻墙格子",
                properties: {
                    theme: FurnitureTheme.Natural,
                    level: 4,
                    value: 45,
                    beauty: 25,
                    isWallAdjacent: true
                }
            },
            {
                id: 21,
                name: "简约飘台",
                type: FurnitureType.WallAdjacent,
                baseSize: new Vec2(3, 1),
                spriteFrame: "texture/furniture/minimalist_bay_window",
                description: "简约风格的飘台，3×1尺寸，只能放在邻墙格子",
                properties: {
                    theme: FurnitureTheme.Minimalist,
                    level: 4,
                    value: 50,
                    beauty: 28,
                    isWallAdjacent: true
                }
            },
            {
                id: 22,
                name: "古典壁炉",
                type: FurnitureType.WallAdjacent,
                baseSize: new Vec2(2, 2),
                spriteFrame: "texture/furniture/classic_fireplace",
                description: "古典风格的壁炉，2×2尺寸，只能放在邻墙格子",
                properties: {
                    theme: FurnitureTheme.Classic,
                    level: 5,
                    value: 80,
                    beauty: 35,
                    isWallAdjacent: true
                }
            },
            {
                id: 23,
                name: "现代电视墙",
                type: FurnitureType.LowWallDecoration,
                baseSize: new Vec2(2, 1),
                spriteFrame: "texture/furniture/modern_tv_wall",
                description: "现代风格的电视墙，2×1尺寸，只能摆放在墙上邻接地面的位置",
                properties: {
                    theme: FurnitureTheme.Modern,
                    level: 4,
                    value: 60,
                    beauty: 30,
                    isLowWallDecoration: true
                }
            },
            {
                id: 24,
                name: "工业风展示架",
                type: FurnitureType.LowWallDecoration,
                baseSize: new Vec2(3, 1),
                spriteFrame: "texture/furniture/industrial_display_rack",
                description: "工业风格的展示架，3×1尺寸，只能摆放在墙上邻接地面的位置",
                properties: {
                    theme: FurnitureTheme.Industrial,
                    level: 3,
                    value: 40,
                    beauty: 20,
                    isLowWallDecoration: true
                }
            },
            {
                id: 25,
                name: "简约投影屏",
                type: FurnitureType.LowWallDecoration,
                baseSize: new Vec2(2, 2),
                spriteFrame: "texture/furniture/minimalist_projector_screen",
                description: "简约风格的投影屏，2×2尺寸，只能摆放在墙上邻接地面的位置",
                properties: {
                    theme: FurnitureTheme.Minimalist,
                    level: 4,
                    value: 70,
                    beauty: 32,
                    isLowWallDecoration: true
                }
            },
            {
                id: 26,
                name: "自然风植物墙架",
                type: FurnitureType.LowWallDecoration,
                baseSize: new Vec2(1, 2),
                spriteFrame: "texture/furniture/natural_plant_wall_rack",
                description: "自然风格的植物墙架，1×2尺寸，只能摆放在墙上邻接地面的位置",
                properties: {
                    theme: FurnitureTheme.Natural,
                    level: 3,
                    value: 35,
                    beauty: 25,
                    isLowWallDecoration: true
                }
            }
        ];
    }

    /**
     * 根据ID获取家具模板
     */
    static getTemplateById(id: number): FurnitureTemplate | null {
        const templates = this.getAllTemplates();
        return templates.find(template => template.id === id) || null;
    }

    /**
     * 根据类型获取家具模板
     */
    static getTemplatesByType(type: FurnitureType): FurnitureTemplate[] {
        const templates = this.getAllTemplates();
        return templates.filter(template => template.type === type);
    }

    /**
     * 根据主题获取家具模板
     */
    static getTemplatesByTheme(theme: FurnitureTheme): FurnitureTemplate[] {
        const templates = this.getAllTemplates();
        return templates.filter(template => template.properties.theme === theme);
    }

    /**
     * 获取挂饰类家具模板
     */
    static getWallDecorationTemplates(): FurnitureTemplate[] {
        const templates = this.getAllTemplates();
        return templates.filter(template => template.properties.isWallDecoration === true);
    }

    /**
     * 获取普通家具模板（非挂饰）
     */
    static getNormalFurnitureTemplates(): FurnitureTemplate[] {
        const templates = this.getAllTemplates();
        return templates.filter(template => template.properties.isWallDecoration !== true);
    }

    /**
     * 获取模板总数
     */
    static getTemplateCount(): number {
        return this.getAllTemplates().length;
    }
}
